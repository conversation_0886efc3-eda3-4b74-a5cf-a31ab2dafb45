# 历史事件页面修改说明

## 修改概述

根据需求，对 `webgl/历史事件.html` 文件进行了以下主要修改：

## 1. API接口集成

### 接口信息
- **接口地址**: `https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list`
- **请求方法**: GET
- **请求头**:
  - `Accept: application/json`
  - `Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg5MWQ5ZWYxLTE3ZjItNGVjYy05ZTU2LTQyZTA0MGQyMmVmMiJ9.C45TcywB-9WRYyfu5a-kkm4C9T5q1TNLygCN4yN3mPm3xRBdd35yD1NNzcsWMpa5ARysZymC6BvFFpWdzXLRoA`

### 请求参数（URL查询参数）
```
GET /prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59
```

**参数说明：**
- `pageNum`: 页码
- `pageSize`: 每页数量
- `alertName`: 事件类型（可选，查询所有事件时不传此参数）
- `params[beginTime]`: 开始时间（可选）
- `params[endTime]`: 结束时间（可选）

### 数据处理
- 使用 `response.rows[].detail.name` 字段作为事件信息显示
- 自动解析 `detail` 字段中的JSON数据
- 过滤掉恢复事件，只保留报警事件和故障事件

## 2. 表格结构修改

### 原表格结构
- 序号 | 时间 | 日期 | 设备 | 告警事件

### 新表格结构
- 序号 | 时间 | 报警事件

### 具体修改
1. **合并时间列**: 将原有的时间列和日期列合并为一个时间列
2. **删除设备列**: 移除了设备相关的显示和筛选功能
3. **修改列名**: 将"告警事件"列名修改为"报警事件"
4. **时间格式**: 统一使用 `YYYY-MM-DD HH:mm:ss` 格式

## 3. 事件类型筛选

### 保留的事件类型
- **报警事件** (`alertName: "报警设备"`)
- **故障事件** (`alertName: "故障设备"`)

### 删除的事件类型
- 恢复事件（已在数据处理阶段过滤）

## 4. 新增功能

### 认证功能
- **JWT Token认证**: 添加Bearer token认证头
- **Token验证**: 页面加载时验证token格式和有效性
- **认证错误处理**: 401/403错误的专门处理和提示
- **配置管理**: 将API配置集中管理，便于维护

### 分页功能
- 显示当前页码和总页数
- 显示记录范围（如：显示 1-50 条，共 135427 条记录）
- 上一页/下一页按钮
- 自动禁用无效的分页按钮

### 错误处理
- 改进的API错误处理
- 认证失败专门提示
- 网络错误重试机制
- 用户友好的错误提示

### 加载状态
- 加载时显示旋转图标
- 显示当前加载的页码信息
- 加载时隐藏分页控件

## 5. 样式优化

### CSS修改
- 调整表格列宽适应新的三列布局
- 添加分页控件样式
- 改进响应式设计
- 优化加载和错误状态显示

### 响应式设计
- 小屏幕下分页控件垂直排列
- 保持整体科技蓝色主题风格

## 6. 数据验证

### 初始化验证
- 验证筛选条件设置
- 检查时间范围合理性
- 控制台输出调试信息

### 运行时验证
- API响应数据格式验证
- 事件数据完整性检查
- 时间格式标准化处理

## 7. 使用说明

### 筛选功能
1. **事件类型筛选**: 点击左侧筛选面板中的事件类型按钮
2. **时间范围筛选**: 设置开始时间和结束时间，点击"应用筛选"
3. **默认时间范围**: 最近7天

### 分页操作
1. 使用底部分页控件进行翻页
2. 每页显示50条记录
3. 显示总记录数和当前页信息

### 事件详情
- 点击任意事件行查看详细信息
- 显示事件ID、类型、名称、时间等完整信息

## 8. 技术实现

### 主要函数
- `loadHistoryEvents()`: API数据加载
- `processAPIData()`: 数据处理和过滤
- `renderEventsList()`: 事件列表渲染
- `addPaginationControls()`: 分页控件生成
- `formatDisplayTime()`: 时间格式化
- `validateAuthToken()`: 认证token验证

### 错误处理
- 网络请求异常处理
- 认证失败专门处理（401/403状态码）
- JSON解析错误处理
- 数据格式验证
- 用户友好的错误提示

## 9. 测试建议

1. **功能测试**
   - 验证API接口调用
   - 测试各种筛选条件
   - 检查分页功能
   - 验证时间格式显示

2. **异常测试**
   - 网络断开情况
   - API返回错误
   - 无数据情况
   - 大数据量加载

3. **兼容性测试**
   - 不同浏览器兼容性
   - 不同屏幕尺寸适配
   - 移动端显示效果

## 10. 注意事项

1. **API依赖**: 页面功能完全依赖于API接口的可用性
2. **数据格式**: 依赖于API返回数据中的 `detail.name` 字段
3. **时间处理**: 自动处理多种时间格式，确保显示一致性
4. **性能考虑**: 大时间范围查询可能影响加载速度

## 修改完成

所有要求的功能已经实现，页面现在可以：
- ✅ 正确调用API接口
- ✅ 处理返回的JSON数据
- ✅ 显示合并后的时间列
- ✅ 隐藏设备相关信息
- ✅ 只显示报警和故障事件
- ✅ 提供分页功能
- ✅ 具备良好的错误处理
- ✅ 保持原有的科技蓝色主题风格
