/**
 * 电气系统数据处理器
 * 负责解析、验证和处理从 MQTT 接收到的电气系统数据
 * 白云电气设备数字孪生系统
 */

class ElectricalDataProcessor {
    constructor() {
        // 物模型数据结构定义（基于实际接收到的数据）
        this.dataSchema = {
            properties: {
                // 电气系统状态参数
                'HMI_30039_0': { name: '起始状态', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_1': { name: '充电', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_2': { name: '单元自检', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_3': { name: '复位', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_4': { name: '就绪', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_5': { name: '运行', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_6': { name: '故障', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_7': { name: '高压', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_8': { name: '开机自检', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_9': { name: '备用', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_10': { name: '合高压等待', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_11': { name: '故障2', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },
                'HMI_30039_12': { name: '复位2', type: 'integer', min: 0, max: 1, unit: '', system: 'electrical' },

                // 电气系统电压参数
                'HMI_32030': { name: '母线电压Uab', type: 'float', min: 0, max: 50, unit: 'kV', system: 'electrical' },
                'HMI_32032': { name: '母线电压Ubc', type: 'float', min: 0, max: 50, unit: 'kV', system: 'electrical' },
                'HMI_32034': { name: '母线电压Uca', type: 'float', min: 0, max: 50, unit: 'kV', system: 'electrical' },

                // 电气系统电流参数
                'HMI_32040': { name: 'SVG电流la', type: 'float', min: -1000, max: 1000, unit: 'A', system: 'electrical' },
                'HMI_32042': { name: 'SVG电流lb', type: 'float', min: -1000, max: 1000, unit: 'A', system: 'electrical' },
                'HMI_32044': { name: 'SVG电流lc', type: 'float', min: -1000, max: 1000, unit: 'A', system: 'electrical' },
                'HMI_32046': { name: '网侧负载无功电流', type: 'float', min: -1000, max: 1000, unit: 'A', system: 'electrical' },

                // 电气系统功率参数
                'HMI_32048': { name: '负载无功功率', type: 'float', min: -100, max: 100, unit: 'MVAr', system: 'electrical' },
                'HMI_32050': { name: '功率因数', type: 'float', min: 0, max: 1, unit: '', system: 'electrical' },

                // 水冷系统状态参数
                'HMI_33525_0': { name: '水冷系统自动模式', type: 'integer', min: 0, max: 100, unit: '', system: 'cooling' },
                'HMI_33525_2': { name: '水冷系统远程控制', type: 'integer', min: 0, max: 100, unit: '', system: 'cooling' },
                'HMI_33525_10': { name: '水冷运行状态', type: 'integer', min: 0, max: 100, unit: '', system: 'cooling' },

                // 水冷系统温度参数
                'HMI_33500': { name: '供水温度', type: 'integer', min: 0, max: 100, unit: '℃', system: 'cooling' },
                'HMI_33501': { name: '回水温度', type: 'integer', min: 0, max: 100, unit: '℃', system: 'cooling' },
                'HMI_33506': { name: '阀室温度', type: 'integer', min: 0, max: 100, unit: '℃', system: 'cooling' },

                // 水冷系统压力参数
                'HMI_33502': { name: '供水压力', type: 'integer', min: 0, max: 100, unit: 'bar', system: 'cooling' },
                'HMI_33503': { name: '回水压力', type: 'integer', min: 0, max: 100, unit: 'bar', system: 'cooling' },

                // 水冷系统流量和其他参数
                'HMI_33504': { name: '冷却水流量', type: 'integer', min: 0, max: 100, unit: 'L/min', system: 'cooling' },
                'HMI_33505': { name: '冷却水电导率', type: 'integer', min: 0, max: 100, unit: 'μs/cm', system: 'cooling' }
            }
        };

        // 数据验证规则
        this.validationRules = {
            required: [], // 不强制要求特定字段，支持多种格式
            optional: ['properties', 'message', 'sources', 'functions', 'events', 'timestamp', 'deviceId']
        };

        // 数据处理统计
        this.statistics = {
            totalReceived: 0,
            validData: 0,
            invalidData: 0,
            lastProcessTime: null,
            processingErrors: []
        };
    }

    /**
     * 处理接收到的 MQTT 数据
     * @param {string|Object} rawData - 原始数据（JSON字符串或对象）
     * @returns {Object} 处理结果
     */
    processData(rawData) {
        this.statistics.totalReceived++;
        this.statistics.lastProcessTime = new Date();

        try {
            // 解析 JSON 数据
            const parsedData = this.parseData(rawData);
            
            // 验证数据格式
            const validationResult = this.validateData(parsedData);
            if (!validationResult.isValid) {
                this.statistics.invalidData++;
                return {
                    success: false,
                    error: 'Data validation failed',
                    details: validationResult.errors,
                    data: null
                };
            }

            // 处理和转换数据
            const processedData = this.transformData(parsedData);
            
            // 验证数据完整性
            const integrityCheck = this.checkDataIntegrity(processedData);
            if (!integrityCheck.isValid) {
                console.warn('数据完整性检查警告:', integrityCheck.warnings);
            }

            this.statistics.validData++;
            
            return {
                success: true,
                data: processedData,
                metadata: {
                    processTime: new Date(),
                    dataQuality: this.assessDataQuality(processedData),
                    integrityCheck: integrityCheck
                }
            };

        } catch (error) {
            this.statistics.invalidData++;
            this.statistics.processingErrors.push({
                timestamp: new Date(),
                error: error.message,
                data: rawData
            });

            console.error('数据处理失败:', error);
            return {
                success: false,
                error: error.message,
                data: null
            };
        }
    }

    /**
     * 解析数据
     * @param {string|Object} rawData - 原始数据
     * @returns {Object} 解析后的数据
     */
    parseData(rawData) {
        if (typeof rawData === 'string') {
            try {
                return JSON.parse(rawData);
            } catch (error) {
                throw new Error(`JSON 解析失败: ${error.message}`);
            }
        } else if (typeof rawData === 'object' && rawData !== null) {
            return rawData;
        } else {
            throw new Error('无效的数据格式');
        }
    }

    /**
     * 验证数据格式
     * @param {Object} data - 要验证的数据
     * @returns {Object} 验证结果
     */
    validateData(data) {
        const errors = [];
        const warnings = [];

        // 检查是否是新的消息格式（包含 message 数组）
        if (data.message && Array.isArray(data.message)) {
            // 新格式验证
            if (data.message.length === 0) {
                warnings.push('消息数组为空');
            }

            // 验证消息数组中的每个项目
            data.message.forEach((item, index) => {
                if (!item.id || !item.name || item.value === undefined) {
                    errors.push(`消息项 ${index} 缺少必需字段 (id, name, value)`);
                }
            });
        } else {
            // 原有格式验证
            // 检查必需字段
            for (const field of this.validationRules.required) {
                if (!data.hasOwnProperty(field)) {
                    errors.push(`缺少必需字段: ${field}`);
                }
            }

            // 验证 properties 字段
            if (data.properties) {
                const propertyValidation = this.validateProperties(data.properties);
                errors.push(...propertyValidation.errors);
                warnings.push(...propertyValidation.warnings);
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    }

    /**
     * 验证属性数据
     * @param {Object} properties - 属性数据
     * @returns {Object} 验证结果
     */
    validateProperties(properties) {
        const errors = [];
        const warnings = [];

        Object.keys(properties).forEach(propertyId => {
            const value = properties[propertyId];
            const schema = this.dataSchema.properties[propertyId];

            if (schema) {
                // 验证数据类型
                if (schema.type === 'integer' && !Number.isInteger(value)) {
                    errors.push(`${propertyId} 应为整数类型，实际值: ${value}`);
                }

                // 验证数值范围
                if (typeof value === 'number') {
                    if (schema.min !== undefined && value < schema.min) {
                        warnings.push(`${propertyId} 值 ${value} 低于最小值 ${schema.min}`);
                    }
                    if (schema.max !== undefined && value > schema.max) {
                        warnings.push(`${propertyId} 值 ${value} 超过最大值 ${schema.max}`);
                    }
                }
            } else {
                warnings.push(`未知的属性ID: ${propertyId}`);
            }
        });

        return { errors, warnings };
    }

    /**
     * 转换数据格式
     * @param {Object} data - 原始数据
     * @returns {Object} 转换后的数据
     */
    transformData(data) {
        const transformed = {
            timestamp: data.timestamp || new Date().toISOString(),
            deviceId: data.deviceId || 'unknown',
            properties: {},
            functions: data.functions || [],
            events: data.events || []
        };

        // 处理新的消息格式
        if (data.message && Array.isArray(data.message)) {
            data.message.forEach(item => {
                const propertyId = item.id;
                const value = this.parseValue(item.value);
                const schema = this.dataSchema.properties[propertyId];

                transformed.properties[propertyId] = {
                    id: propertyId,
                    name: item.name || (schema ? schema.name : propertyId),
                    value: value,
                    unit: schema ? schema.unit : '',
                    type: schema ? schema.type : this.inferType(value),
                    isValid: this.isValueValid(value, schema),
                    processedAt: new Date().toISOString(),
                    originalTimestamp: item.ts
                };
            });
        } else if (data.properties) {
            // 处理原有的 properties 格式
            Object.keys(data.properties).forEach(propertyId => {
                const value = data.properties[propertyId];
                const schema = this.dataSchema.properties[propertyId];

                transformed.properties[propertyId] = {
                    id: propertyId,
                    name: schema ? schema.name : propertyId,
                    value: value,
                    unit: schema ? schema.unit : '',
                    type: schema ? schema.type : 'unknown',
                    isValid: this.isValueValid(value, schema),
                    processedAt: new Date().toISOString()
                };
            });
        }

        return transformed;
    }

    /**
     * 解析值，处理字符串数字
     * @param {*} value - 原始值
     * @returns {*} 解析后的值
     */
    parseValue(value) {
        if (typeof value === 'string') {
            // 尝试转换为数字
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
                return numValue;
            }
        }
        return value;
    }

    /**
     * 推断数据类型
     * @param {*} value - 值
     * @returns {string} 推断的类型
     */
    inferType(value) {
        if (Number.isInteger(value)) {
            return 'integer';
        } else if (typeof value === 'number') {
            return 'float';
        } else if (typeof value === 'string') {
            return 'string';
        } else if (typeof value === 'boolean') {
            return 'boolean';
        }
        return 'unknown';
    }

    /**
     * 检查数据完整性
     * @param {Object} data - 处理后的数据
     * @returns {Object} 完整性检查结果
     */
    checkDataIntegrity(data) {
        const warnings = [];
        const expectedProperties = Object.keys(this.dataSchema.properties);
        const receivedProperties = Object.keys(data.properties || {});

        // 检查缺失的属性
        const missingProperties = expectedProperties.filter(prop => 
            !receivedProperties.includes(prop)
        );

        if (missingProperties.length > 0) {
            warnings.push(`缺失属性: ${missingProperties.join(', ')}`);
        }

        // 检查数据时效性
        const dataAge = Date.now() - new Date(data.timestamp).getTime();
        if (dataAge > 60000) { // 1分钟
            warnings.push(`数据可能过时，时间差: ${Math.round(dataAge / 1000)}秒`);
        }

        return {
            isValid: warnings.length === 0,
            warnings: warnings,
            completeness: receivedProperties.length / expectedProperties.length,
            dataAge: dataAge
        };
    }

    /**
     * 评估数据质量
     * @param {Object} data - 处理后的数据
     * @returns {Object} 数据质量评估
     */
    assessDataQuality(data) {
        const properties = data.properties || {};
        const totalProperties = Object.keys(properties).length;
        const validProperties = Object.values(properties).filter(prop => prop.isValid).length;

        const quality = {
            score: totalProperties > 0 ? (validProperties / totalProperties) * 100 : 0,
            level: 'unknown',
            validProperties: validProperties,
            totalProperties: totalProperties,
            issues: []
        };

        // 确定质量等级
        if (quality.score >= 95) {
            quality.level = 'excellent';
        } else if (quality.score >= 80) {
            quality.level = 'good';
        } else if (quality.score >= 60) {
            quality.level = 'fair';
        } else {
            quality.level = 'poor';
        }

        // 收集质量问题
        Object.values(properties).forEach(prop => {
            if (!prop.isValid) {
                quality.issues.push(`${prop.name} (${prop.id}) 数据无效`);
            }
        });

        return quality;
    }

    /**
     * 验证单个值是否有效
     * @param {*} value - 要验证的值
     * @param {Object} schema - 数据模式
     * @returns {boolean} 是否有效
     */
    isValueValid(value, schema) {
        if (!schema) return true;

        // 检查数据类型
        if (schema.type === 'integer' && !Number.isInteger(value)) {
            return false;
        }

        // 检查数值范围
        if (typeof value === 'number') {
            if (schema.min !== undefined && value < schema.min) {
                return false;
            }
            if (schema.max !== undefined && value > schema.max) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取处理统计信息
     * @returns {Object} 统计信息
     */
    getStatistics() {
        return {
            ...this.statistics,
            successRate: this.statistics.totalReceived > 0 ? 
                (this.statistics.validData / this.statistics.totalReceived) * 100 : 0
        };
    }

    /**
     * 重置统计信息
     */
    resetStatistics() {
        this.statistics = {
            totalReceived: 0,
            validData: 0,
            invalidData: 0,
            lastProcessTime: null,
            processingErrors: []
        };
    }

    /**
     * 获取支持的属性列表
     * @returns {Array} 属性列表
     */
    getSupportedProperties() {
        return Object.keys(this.dataSchema.properties).map(id => ({
            id: id,
            name: this.dataSchema.properties[id].name,
            type: this.dataSchema.properties[id].type,
            unit: this.dataSchema.properties[id].unit
        }));
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ElectricalDataProcessor;
} else {
    window.ElectricalDataProcessor = ElectricalDataProcessor;
}
