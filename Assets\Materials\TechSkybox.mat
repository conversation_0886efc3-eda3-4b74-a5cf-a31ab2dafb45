%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: TechSkybox
  m_Shader: {fileID: 106, guid: 0000000000000000f000000000000000, type: 0}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _SUNDISK_HIGH_QUALITY
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs: []
    m_Ints: []
    m_Floats:
    - _AtmosphereThickness: 0.8
    - _Exposure: 1.2
    - _SunDisk: 2
    - _SunSize: 0.04
    - _SunSizeConvergence: 5
    m_Colors:
    - _GroundColor: {r: 0.1764706, g: 0.1764706, b: 0.1764706, a: 1}
    - _SkyTint: {r: 0.05, g: 0.1, b: 0.3, a: 1}
    - _SunColor: {r: 0.3, g: 0.5, b: 1, a: 1}
  m_BuildTextureStacks: []
