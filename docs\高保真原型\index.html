<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白云电气设备数字孪生系统 - 高保真原型</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    <!-- 引入 ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 主容器 -->
    <div id="app" class="app-container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-bolt"></i>
                    <span class="logo-text">白云电气</span>
                </div>
                <div class="system-title">设备数字孪生监控系统</div>
            </div>
            <div class="header-center">
                <div class="time-display" id="currentTime"></div>
            </div>
            <div class="header-right">
                <div class="status-indicators">
                    <div class="status-item online">
                        <i class="fas fa-circle"></i>
                        <span>系统在线</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-users"></i>
                        <span>3人在线</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧控制面板 -->
            <aside class="left-panel">
                <div class="panel-header">
                    <h3>设备控制</h3>
                </div>
                <div class="control-section">
                    <div class="control-group">
                        <h4>视角控制</h4>
                        <div class="control-buttons">
                            <button class="control-btn primary" id="overviewBtn">
                                <i class="fas fa-eye"></i>
                                总览视角
                            </button>
                            <button class="control-btn" id="tourBtn">
                                <i class="fas fa-route"></i>
                                自动漫游
                            </button>
                            <button class="control-btn" id="expandBtn">
                                <i class="fas fa-expand-arrows-alt"></i>
                                设备展开
                            </button>
                        </div>
                    </div>
                    
                    <div class="control-group">
                        <h4>设备状态</h4>
                        <div class="device-status-list">
                            <div class="device-item">
                                <div class="device-name">主变压器</div>
                                <div class="device-status running">运行中</div>
                            </div>
                            <div class="device-item">
                                <div class="device-name">开关柜A</div>
                                <div class="device-status running">运行中</div>
                            </div>
                            <div class="device-item">
                                <div class="device-name">开关柜B</div>
                                <div class="device-status warning">告警</div>
                            </div>
                            <div class="device-item">
                                <div class="device-name">保护装置</div>
                                <div class="device-status running">运行中</div>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 中央3D展示区域 -->
            <section class="center-panel">
                <div class="unity-container" id="unityContainer">
                    <!-- Unity WebGL 内容将在这里加载 -->
                    <div class="unity-placeholder">
                        <div class="placeholder-content">
                            <i class="fas fa-cube rotating"></i>
                            <h3>3D模型加载中...</h3>
                            <p>正在初始化Unity WebGL引擎</p>
                            <div class="loading-bar">
                                <div class="loading-progress" id="loadingProgress"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 3D场景控制工具栏 -->
                <div class="scene-toolbar">
                    <div class="toolbar-group">
                        <button class="toolbar-btn" title="重置视角">
                            <i class="fas fa-home"></i>
                        </button>
                        <button class="toolbar-btn" title="全屏显示">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="toolbar-btn" title="截图">
                            <i class="fas fa-camera"></i>
                        </button>
                    </div>
                </div>
            </section>

            <!-- 右侧数据面板 -->
            <aside class="right-panel">
                <div class="panel-header">
                    <h3>实时监控</h3>
                </div>
                
                <!-- 关键指标卡片 -->
                <div class="metrics-cards">
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">220.5V</div>
                            <div class="metric-label">电压</div>
                        </div>
                        <div class="metric-trend up">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-thermometer-half"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">35.2°C</div>
                            <div class="metric-label">温度</div>
                        </div>
                        <div class="metric-trend stable">
                            <i class="fas fa-minus"></i>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">87%</div>
                            <div class="metric-label">负载率</div>
                        </div>
                        <div class="metric-trend down">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 实时数据图表区域 -->
                <div class="charts-section">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h4>电压趋势</h4>
                            <div class="chart-controls">
                                <button class="chart-btn active">1H</button>
                                <button class="chart-btn">6H</button>
                                <button class="chart-btn">24H</button>
                            </div>
                        </div>
                        <div class="chart" id="voltageChart"></div>
                    </div>
                    
                    <div class="chart-container">
                        <div class="chart-header">
                            <h4>温度监测</h4>
                        </div>
                        <div class="chart" id="temperatureChart"></div>
                    </div>
                </div>
            </aside>
        </main>

        <!-- 底部状态栏 -->
        <footer class="footer">
            <div class="footer-left">
                <div class="system-info">
                    <span>系统版本: v2.1.0</span>
                    <span>|</span>
                    <span>最后更新: 2024-01-15 14:30:25</span>
                </div>
            </div>
            <div class="footer-center">
                <div class="connection-status">
                    <i class="fas fa-wifi"></i>
                    <span>连接状态: 正常</span>
                    <div class="signal-strength">
                        <div class="signal-bar active"></div>
                        <div class="signal-bar active"></div>
                        <div class="signal-bar active"></div>
                        <div class="signal-bar"></div>
                    </div>
                </div>
            </div>
            <div class="footer-right">
                <div class="copyright">
                    © 2024 白云电气集团 - 数字孪生监控系统
                </div>
            </div>
        </footer>
    </div>

    <!-- 弹窗和模态框 -->
    <div id="alertModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>系统告警</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="alert-item">
                    <div class="alert-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">开关柜B温度异常</div>
                        <div class="alert-time">2024-01-15 14:25:30</div>
                        <div class="alert-desc">检测到开关柜B温度超过阈值，当前温度: 45.2°C</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 文件 -->
    <script src="scripts/main.js"></script>
    <script src="scripts/charts.js"></script>
    <script src="scripts/unity-integration.js"></script>
</body>
</html>
