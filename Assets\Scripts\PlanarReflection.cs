using UnityEngine;

public class PlanarReflection : MonoBehaviour
{
    public Camera reflectionCamera;
    public LayerMask reflectionLayers = -1;
    public float clipPlaneOffset = 0.07f;
    
    private RenderTexture reflectionTexture;
    private Material reflectionMaterial;
    private static bool isReflecting = false;
    
    void Start()
    {
        // 创建反射相机
        if (reflectionCamera == null)
        {
            GameObject reflCamObj = new GameObject("ReflectionCamera");
            reflCamObj.transform.parent = transform;
            reflectionCamera = reflCamObj.AddComponent<Camera>();
            reflectionCamera.enabled = false;
        }
        
        // 创建反射材质
        Renderer rend = GetComponent<Renderer>();
        if (rend)
        {
            reflectionMaterial = rend.material;
        }
    }
    
    void OnWillRenderObject()
    {
        if (!enabled || !reflectionMaterial || isReflecting)
            return;
        
        isReflecting = true;
        
        Camera cam = Camera.current;
        if (!cam)
            return;
        
        // 确保摄像机处于有效状态
        if (!cam.isActiveAndEnabled || cam.stereoEnabled)
        {
            isReflecting = false;
            return;
        }
    
        // 创建反射纹理
        if (reflectionTexture == null || reflectionTexture.width != Screen.width || reflectionTexture.height != Screen.height)
        {
            if (reflectionTexture)
                DestroyImmediate(reflectionTexture);
            
            reflectionTexture = new RenderTexture(Screen.width, Screen.height, 16);
            reflectionTexture.name = "ReflectionTexture";
            reflectionTexture.isPowerOfTwo = true;
            reflectionTexture.hideFlags = HideFlags.DontSave;
            
            // 设置材质的反射纹理
            reflectionMaterial.SetTexture("_ReflectionTex", reflectionTexture);
        }
        
        // 设置反射相机
        reflectionCamera.CopyFrom(cam);
        reflectionCamera.cullingMask = reflectionLayers;
        
        // 反射矩阵
        Vector3 pos = transform.position;
        Vector3 normal = transform.up;
        float d = -Vector3.Dot(normal, pos) - clipPlaneOffset;
        Vector4 reflectionPlane = new Vector4(normal.x, normal.y, normal.z, d);
        
        Matrix4x4 reflection = Matrix4x4.zero;
        CalculateReflectionMatrix(ref reflection, reflectionPlane);
        
        Vector3 oldPos = cam.transform.position;
        Vector3 newPos = reflection.MultiplyPoint(oldPos);
        reflectionCamera.worldToCameraMatrix = cam.worldToCameraMatrix * reflection;
        
        // 设置投影矩阵以处理裁剪平面
        Vector4 clipPlane = CameraSpacePlane(reflectionCamera, pos, normal, 1.0f, clipPlaneOffset);
        Matrix4x4 projection = cam.projectionMatrix;
        CalculateObliqueMatrix(ref projection, clipPlane);
        reflectionCamera.projectionMatrix = projection;
        
        reflectionCamera.transform.position = newPos;
        Vector3 euler = cam.transform.eulerAngles;
        reflectionCamera.transform.eulerAngles = new Vector3(-euler.x, euler.y, euler.z);
        
        // 确保摄像机位置有效
        if (float.IsNaN(newPos.x) || float.IsNaN(newPos.y) || float.IsNaN(newPos.z))
        {
            isReflecting = false;
            return;
        }
    
        // 渲染到纹理
        reflectionCamera.targetTexture = reflectionTexture;
        GL.invertCulling = true;
        reflectionCamera.Render();
        GL.invertCulling = false;
        
        isReflecting = false;
    }
    
    // 计算反射矩阵
    private void CalculateReflectionMatrix(ref Matrix4x4 reflectionMat, Vector4 plane)
    {
        reflectionMat.m00 = (1F - 2F * plane[0] * plane[0]);
        reflectionMat.m01 = (-2F * plane[0] * plane[1]);
        reflectionMat.m02 = (-2F * plane[0] * plane[2]);
        reflectionMat.m03 = (-2F * plane[3] * plane[0]);

        reflectionMat.m10 = (-2F * plane[1] * plane[0]);
        reflectionMat.m11 = (1F - 2F * plane[1] * plane[1]);
        reflectionMat.m12 = (-2F * plane[1] * plane[2]);
        reflectionMat.m13 = (-2F * plane[3] * plane[1]);

        reflectionMat.m20 = (-2F * plane[2] * plane[0]);
        reflectionMat.m21 = (-2F * plane[2] * plane[1]);
        reflectionMat.m22 = (1F - 2F * plane[2] * plane[2]);
        reflectionMat.m23 = (-2F * plane[3] * plane[2]);

        reflectionMat.m30 = 0F;
        reflectionMat.m31 = 0F;
        reflectionMat.m32 = 0F;
        reflectionMat.m33 = 1F;
    }
    
    // 计算相机空间中的平面
    private Vector4 CameraSpacePlane(Camera cam, Vector3 pos, Vector3 normal, float sideSign, float clipPlaneOffset)
    {
        Vector3 offsetPos = pos + normal * clipPlaneOffset;
        Matrix4x4 m = cam.worldToCameraMatrix;
        Vector3 cpos = m.MultiplyPoint(offsetPos);
        Vector3 cnormal = m.MultiplyVector(normal).normalized * sideSign;
        return new Vector4(cnormal.x, cnormal.y, cnormal.z, -Vector3.Dot(cpos, cnormal));
    }
    
    // 计算斜投影矩阵
    private void CalculateObliqueMatrix(ref Matrix4x4 projection, Vector4 clipPlane)
    {
        Vector4 q = projection.inverse * new Vector4(
            Mathf.Sign(clipPlane.x),
            Mathf.Sign(clipPlane.y),
            1.0f,
            1.0f
        );
        Vector4 c = clipPlane * (2.0F / Vector4.Dot(clipPlane, q));
        projection[2] = c.x - projection[3];
        projection[6] = c.y - projection[7];
        projection[10] = c.z - projection[11];
        projection[14] = c.w - projection[15];
    }
}