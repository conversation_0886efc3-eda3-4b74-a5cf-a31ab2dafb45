# CORS跨域问题解决方案

## 问题描述

当前页面在请求API时遇到CORS（跨域资源共享）错误，这是浏览器的安全机制阻止了跨域请求。

### 错误信息
```
CORS error: 跨域请求被阻止
```

### 技术原因
- **当前页面域名**: `file://` 或 `http://localhost` 或其他域名
- **API服务器域名**: `https://exdraw.qizhiyun.cc`
- **请求方式**: GET（已从POST改为GET，减少CORS复杂性）
- **问题**: 浏览器阻止不同域名之间的HTTP请求

## 解决方案

### 方案1: 服务器端配置CORS（推荐）

**需要后端开发者在服务器上添加以下配置：**

#### Spring Boot配置示例
```java
@Configuration
public class CorsConfig {
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "OPTIONS")); // 主要需要GET方法
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
```

#### Nginx配置示例
```nginx
location /prod-api/ {
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
    
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type 'text/plain; charset=utf-8';
        add_header Content-Length 0;
        return 204;
    }
    
    proxy_pass http://backend-server;
}
```

### 方案2: 使用代理服务器

#### 开发环境 - 使用本地代理
创建一个简单的代理服务器：

```javascript
// proxy-server.js
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const app = express();

// 允许跨域
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// 代理API请求
app.use('/api', createProxyMiddleware({
    target: 'https://exdraw.qizhiyun.cc/prod-api',
    changeOrigin: true,
    pathRewrite: {
        '^/api': ''
    }
}));

app.listen(3001, () => {
    console.log('代理服务器运行在 http://localhost:3001');
});
```

然后修改前端代码中的API地址：
```javascript
const API_CONFIG = {
    baseUrl: 'http://localhost:3001/api', // 使用代理服务器
    // ... 其他配置
};
```

### 方案3: 部署到相同域名

将前端页面部署到与API相同的域名下：
- 将页面部署到 `https://exdraw.qizhiyun.cc` 的某个路径下
- 这样就不存在跨域问题

### 方案4: 使用模拟数据模式（临时解决方案）

**用于在CORS问题解决前测试页面功能**

页面已内置模拟数据功能：
1. 当遇到CORS错误时，点击"使用模拟数据"按钮
2. 页面将使用本地生成的测试数据
3. 可以测试所有页面功能（筛选、分页、显示等）
4. 右上角会显示"模拟数据模式"标识

**模拟数据特点：**
- 包含报警设备和故障设备两种类型
- 随机生成最近7天内的事件时间
- 支持分页功能（每页50条）
- 包含真实的事件名称和设备信息

### 方案5: 浏览器插件（仅开发测试）

**注意：此方案仅用于开发测试，不适用于生产环境**

安装浏览器插件禁用CORS检查：
- Chrome: "CORS Unblock" 或 "Disable CORS"
- Firefox: "CORS Everywhere"

## 当前页面的改进

已在页面中添加了以下改进：

### 1. CORS错误检测
```javascript
// 自动检测CORS错误并提供解决方案提示
if (error.message.includes('CORS') || error.message.includes('fetch')) {
    errorMessage = 'CORS跨域请求被阻止，请联系管理员配置服务器允许跨域访问';
}
```

### 2. CORS状态检测功能
- 点击"检测CORS状态"按钮
- 自动发送OPTIONS请求测试CORS配置
- 显示详细的检测结果和建议

### 3. 详细的错误提示
- 显示当前页面域名和API服务器域名
- 提供具体的解决方案建议
- 包含技术人员联系建议

### 4. 模拟数据模式
- 内置测试数据生成器
- 支持所有页面功能测试
- 一键切换真实API和模拟数据
- 模拟数据模式指示器

## 测试步骤

### 1. 确认CORS错误
1. 打开浏览器开发者工具（F12）
2. 切换到Console标签页
3. 刷新页面，查看是否有CORS相关错误

### 2. 使用内置检测工具
1. 在页面错误提示中点击"检测CORS状态"
2. 查看检测结果
3. 根据结果采取相应措施

### 3. 验证解决方案
1. 实施上述任一解决方案
2. 刷新页面测试
3. 确认API请求成功

## 联系信息

如需解决CORS问题，请联系：
- **后端开发者**: 配置服务器CORS设置
- **运维人员**: 配置Nginx或其他代理服务器
- **项目管理员**: 协调部署方案

## 技术支持

### 常见问题
1. **Q**: 为什么会有CORS限制？
   **A**: 这是浏览器的安全机制，防止恶意网站访问其他域名的资源

2. **Q**: 能否在前端代码中解决CORS问题？
   **A**: 不能，CORS限制只能通过服务器端配置解决

3. **Q**: 开发环境和生产环境都需要配置CORS吗？
   **A**: 是的，只要存在跨域请求就需要配置

### 调试技巧
1. 查看Network标签页的请求状态
2. 检查Response Headers中的CORS相关头信息
3. 使用curl命令测试API接口的CORS配置

```bash
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list
```
