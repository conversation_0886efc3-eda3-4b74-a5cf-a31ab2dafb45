# 设备交互控制系统使用说明


## git控制
仓库地址：*************:panda_shi/Baiyun-Electric-3D-Large-Screen.git
clone后复制模型： \\QZNAS\PublicToAll\202503桂林白云10kv_svg柜体模型.fbx 放到 Assets\ 目录下

## 概述

目前实现了设备的交互功能，包括：
1. 设备元器件沿设备中心向四周散开和复原
2. 设备开关控制
3. 门的开关控制
4. 水管水流效果
5. 状态显示器闪烁、变色效果
6. Main Camera支持按键控制：
   - wasdqe 控制镜头移动
   - 鼠标左键控制镜头旋转
   - 鼠标滚轮控制镜头缩放
   - 长按鼠标滚轮镜头移动
   - 单击中键还原到初始位置
7. 3D物体开关与2D Canvas上的说明显示连线，在旋转过程中保持连线正确，3D物体开关则隐藏连线和说明
8. CameraController增加两类位置管理：
   - FirstCameraInitialPosition：用于总览的初始化位置，F1键（webgl: ctrl+F1）切换到该位置
   - DeviceCameraPosition：一些位置列表，用于设备的3D视角展示，F2键（webgl: ctrl+F2）开始和终止漫游
   - 支持在editor界面中编辑这些位置，可直接添加当前相机位置
9. [[webgl/index.html]]参考[[webgl/大屏效果图.png]]的风格，实现一下功能：
   - 顶部
      - 标题为“白云电气设备数字孪生系统”
      - 实现总览、漫游功能，直接调用 CameraController 接口
   - 周围显示一些echarts图表
10. [[webgl/index.html]]增加一个按钮
 - 默认为显示“展开”
 - 点击后执行 DeviceController 中的 `ToggleExpand` 方法，显示“收起”
 - 再次点击后后执行 DeviceController 中的 `ToggleExpand` 方法，恢复“展开”
11. 优化[[CameraController.cs]]的漫游功能：
 - 漫游状态下隐藏网页的echarts图表，打开Canvas
 - 漫游停止后隐藏Canvas，显示网页的echarts图表
12. 绘制所有水管中水的流动。
   - 删除[[DeviceController.cs]]中的在编辑器中查找水管的逻辑，改用直接查找（水管都放在了layer:Water里面 ）
   - 支持设置水管流向
13. 在模型`#dataDisplay`上建立屏幕，显示相关数据数据。
   - 开关开启的时候显示`设备已启动`，开关关闭的时候显示`设备待机`；
   - 离开漫游的第一个位置（即开关位置）时，如果开关没有打开，则自动打开开关。
14. 优化大小 —— Build产物从164M优化得到25.5M。
  - 在blender里面优化模型，减少冗余顶点和面数 —— 减面50%，尺寸从151M减小到61.5，但效果太差，不考虑。
  - 在Unity里面优化：
    - 启用gzip压缩，压缩后100M，效果一致。（可能会遇到idm这类软件干扰）
      - Mesh Compression：选择High ———— 压缩包从100M减小到61.8M，效果一致。
      - 取消Import Cameras/Import Lights； ———— 压缩包继续缩小到52.5M，效果一致。
      - Normals(法线)从Import改为Calculate；Tangents（切线）改为None ———— 压缩包继续缩小到51M，效果一致。
    - 启用Brotli压缩，构建耗时久，压缩后78.2M，效果一致。（Chrome、Firefox 只在 HTTPS 协议下支持 Brotli）
      - 经过如上步骤，压缩包减小到25.5M，效果一致。**其中webgl.data 21.1M，比glb格式的23.4M还小一些。**

## 待办列表
- [ ] 体验unity性能测试工具，测试性能。
- [ ] 用threejs+glb格式的模型，重写实现效果试试。 
- [ ] 修改围绕中心点旋转的逻辑，感觉不流畅。
- [ ] 优化大屏外观：按钮移动到中间，最右侧显示当前时间。

## 演示运行
演示地址： http://************:62411/index.html
运行服务器:
1. 安装 Express（如果尚未安装）： `npm install express`
2. 执行：`node server.js`

## 板子上的账号/密码
8/262143


## 脚本组件说明

### CameraController.cs

主要控制相机的移动、旋转、缩放和位置管理功能，需要挂载在Main Camera物体上。

#### 配置说明：

1. **移动设置**
   - `moveSpeed`: 摄像机移动速度
   - `sprintMultiplier`: 按住Shift键时的加速倍率

2. **旋转设置**
   - `horizontalRotationSpeed`: 摄像机水平旋转灵敏度
   - `verticalRotationSpeed`: 摄像机垂直旋转灵敏度
   - `minVerticalAngle`: 垂直视角限制的最小角度
   - `maxVerticalAngle`: 垂直视角限制的最大角度

3. **缩放设置**
   - `zoomSpeed`: 摄像机缩放速度
   - `minFieldOfView`: 最小视野角度
   - `maxFieldOfView`: 最大视野角度

4. **平移设置**
   - `panSpeed`: 鼠标中键平移速度

5. **位置管理**
   - `firstCameraInitialPosition`: 总览初始位置，F1键切换到该位置
   - `deviceCameraPositions`: 设备视角位置列表，F2键开始和终止漫游
   - `positionTransitionSpeed`: 相机位置切换速度
   - `rotationTransitionSpeed`: 相机旋转切换速度
   - `fovTransitionSpeed`: 视野切换速度
   - `deviceViewDwellTime`: 设备视角漫游间隔时间(秒)

#### 使用方法：

1. **基本控制**
   - WASD键：控制相机前后左右移动
   - QE键：控制相机上下移动
   - 鼠标左键拖动：旋转视角
   - 鼠标滚轮：缩放视野
   - 鼠标中键拖动：平移视角
   - 鼠标中键单击：重置到初始位置

2. **位置管理**
   - F1键：切换到总览初始位置
   - F2键：开始/终止设备视角漫游

3. **编辑器扩展**
   - 在Inspector中可以直接设置当前相机位置为总览初始位置
   - 可以添加当前相机位置到设备视角列表
   - 支持在场景视图中直观查看所有相机位置（蓝色球体表示总览位置，绿色球体表示设备视角位置）

### DeviceController.cs

主要控制设备的所有交互功能，需要挂载在设备根物体上。

#### 配置说明：

1. **设备组件**
   - `deviceLogo`: 设备Logo对象，点击后触发展开/收回
   - `deviceComponents`: 需要展开/收回的元器件列表
   - `deviceSwitch`: 设备开关对象
   - `deviceDoor`: 设备门对象
   - `inletPipe`: 进水管对象 (DN50-4.STEP-1.001)
   - `outletPipe`: 出水管对象 (DN50-4.STEP-1)
   - `statusDisplays`: 状态显示器对象列表

2. **动画设置**
   - `expandDuration`: 元器件展开/收回动画时间
   - `expandDistance`: 元器件展开时的最大距离
   - `doorAnimDuration`: 门开关动画时间
   - `doorOpenAngle`: 门开启角度

3. **效果设置**
   - `waterFlowMaterial`: 水流动画材质
   - `blinkFrequency`: 状态显示器闪烁频率
   - `normalColor`: 状态显示器正常颜色
   - `warningColor`: 状态显示器警告颜色
   - `errorColor`: 状态显示器错误颜色

### InteractionManager.cs

管理设备交互和UI显示，需要挂载在场景中的管理器物体上。

#### 配置说明：

1. **设备控制**
   - `deviceController`: 设备控制器引用

2. **UI组件**
   - `dataPanel`: 功能区域数据面板
   - `dataPanelTitle`: 数据面板标题
   - `dataPanelContent`: 数据面板内容

3. **功能区域**
   - `functionalAreas`: 可交互的功能区域列表

## 着色器说明

### WaterFlow.shader

用于实现水管流动效果的着色器。

### StatusDisplay.shader

用于实现状态显示器闪烁和变色效果的着色器。

## 连线功能说明

### LineManager.cs

连线管理器，负责处理3D物体与2D Canvas UI元素之间的连线位置更新和渲染。

#### 配置说明：

1. **连线设置**
   - `connectionLines`: 连线列表，每个连线包含3D物体、UI元素和连线对象
   - `targetCanvas`: 连线所在的Canvas
   - `mainCamera`: 用于世界坐标到屏幕坐标转换的相机
   - `updateFrequency`: 每秒更新连线位置的次数

### LineConnector.cs

连线连接器，用于在设备控制器中集成连线管理功能，简化连线的创建和管理。

#### 配置说明：

1. **连线管理**
   - `lineManager`: 连线管理器引用
   - `connections`: 设备与UI的连接配置列表
   - `targetCanvas`: 连线所在的Canvas

## 使用步骤

1. 在场景中创建一个空物体作为管理器，挂载 `InteractionManager` 脚本
2. 在设备模型根物体上挂载 `DeviceController` 脚本
3. 配置 `DeviceController` 中的各个组件引用：
   - 将设备Logo、开关、门、水管等组件拖拽到对应字段
   - 将需要展开/收回的元器件添加到 `deviceComponents` 列表
   - 将状态显示器添加到 `statusDisplays` 列表
4. 创建水流材质，使用 `WaterFlow` 着色器，并将其分配给 `waterFlowMaterial` 字段
5. 创建状态显示器材质，使用 `StatusDisplay` 着色器，并应用到状态显示器对象
6. 配置 `InteractionManager` 中的UI组件和功能区域
7. 设置3D物体与UI连线功能：
   - 在场景中创建一个空物体，添加 `LineManager` 组件
   - 在设备根物体上添加 `LineConnector` 组件
   - 创建连线预制体（可使用编辑器工具：工具 > 创建连线预制体）
   - 在 `LineConnector` 中配置3D物体与UI元素的连接关系
   - 确保Canvas的Render Mode设置为Screen Space - Camera或World Space

## 性能优化建议

1. 对于大型模型（如151M的设备模型），建议：
   - 使用LOD（细节层次）系统减少远处模型的渲染复杂度
   - 合并静态元器件的网格以减少绘制调用
   - 对不需要单独交互的小型元器件进行批处理
2. 使用对象池管理频繁创建/销毁的对象
3. 优化着色器复杂度，特别是在移动平台上
4. 使用GPU Instancing技术渲染相似的元器件

## 常见问题

1. **元器件展开动画不正确**：确保设备中心点设置正确，元器件初始位置已正确记录
2. **水流效果不显示**：检查水流材质是否正确分配，着色器是否支持当前渲染管线
3. **状态显示器不闪烁**：确保状态显示器材质已启用自发光属性，并且已添加到状态显示器列表中
4. **性能问题**：对于大型模型，考虑简化几何体，优化材质和纹理，使用遮挡剔除

## 扩展功能

1. 添加粒子效果增强水流视觉效果
2. 实现更复杂的元器件动画，如旋转或缩放
3. 添加声音反馈增强交互体验
4. 实现数据可视化图表显示实时数据