/**
 * 图表管理模块 - 基于ECharts的数据可视化
 * 白云电气设备数字孪生系统高保真原型
 */

class ChartManager {
    constructor() {
        this.charts = {};
        this.chartData = {
            voltage: this.generateTimeSeriesData(24, 215, 225),
            temperature: this.generateTimeSeriesData(24, 30, 45),
            loadRate: this.generateTimeSeriesData(24, 60, 95),
            deviceStatus: {
                labels: ['主变压器', '开关柜A', '开关柜B', '保护装置'],
                values: [95, 88, 75, 92]
            }
        };
        
        this.init();
    }

    /**
     * 初始化图表管理器
     */
    init() {
        // 等待DOM加载完成后初始化图表
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeAllCharts();
            });
        } else {
            this.initializeAllCharts();
        }
    }

    /**
     * 初始化所有图表
     */
    initializeAllCharts() {
        this.initVoltageChart();
        this.initTemperatureChart();
        
        // 启动数据更新
        this.startDataUpdate();
        
        console.log('所有图表初始化完成');
    }

    /**
     * 初始化电压趋势图表
     */
    initVoltageChart() {
        const container = document.getElementById('voltageChart');
        if (!container) return;

        this.charts.voltage = echarts.init(container);
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(26, 31, 46, 0.9)',
                borderColor: '#00d4ff',
                borderWidth: 1,
                textStyle: {
                    color: '#ffffff'
                },
                formatter: function(params) {
                    const param = params[0];
                    return `${param.seriesName}<br/>
                            时间: ${param.name}<br/>
                            电压: ${param.value}V`;
                }
            },
            grid: {
                left: '5%',
                right: '5%',
                bottom: '10%',
                top: '10%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: this.generateTimeLabels(24),
                axisLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.3)'
                    }
                },
                axisLabel: {
                    color: '#b8c5d6',
                    fontSize: 10
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.1)',
                        type: 'dashed'
                    }
                }
            },
            yAxis: {
                type: 'value',
                min: 210,
                max: 230,
                axisLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.3)'
                    }
                },
                axisLabel: {
                    color: '#b8c5d6',
                    fontSize: 10,
                    formatter: '{value}V'
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(0, 212, 255, 0.1)',
                        type: 'dashed'
                    }
                }
            },
            series: [{
                name: '电压',
                type: 'line',
                data: this.chartData.voltage,
                smooth: true,
                symbol: 'circle',
                symbolSize: 4,
                lineStyle: {
                    color: '#00d4ff',
                    width: 2
                },
                itemStyle: {
                    color: '#00d4ff',
                    borderColor: '#ffffff',
                    borderWidth: 1
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(0, 212, 255, 0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(0, 212, 255, 0.05)'
                        }]
                    }
                },
                emphasis: {
                    itemStyle: {
                        color: '#66e0ff',
                        borderColor: '#ffffff',
                        borderWidth: 2,
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 212, 255, 0.5)'
                    }
                }
            }],
            animation: true,
            animationDuration: 1000,
            animationEasing: 'cubicOut'
        };

        this.charts.voltage.setOption(option);
    }

    /**
     * 初始化温度监测图表
     */
    initTemperatureChart() {
        const container = document.getElementById('temperatureChart');
        if (!container) return;

        this.charts.temperature = echarts.init(container);
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(26, 31, 46, 0.9)',
                borderColor: '#ff7043',
                borderWidth: 1,
                textStyle: {
                    color: '#ffffff'
                },
                formatter: function(params) {
                    const param = params[0];
                    return `${param.seriesName}<br/>
                            时间: ${param.name}<br/>
                            温度: ${param.value}°C`;
                }
            },
            grid: {
                left: '5%',
                right: '5%',
                bottom: '10%',
                top: '10%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: this.generateTimeLabels(24),
                axisLine: {
                    lineStyle: {
                        color: 'rgba(255, 112, 67, 0.3)'
                    }
                },
                axisLabel: {
                    color: '#b8c5d6',
                    fontSize: 10
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: 'rgba(255, 112, 67, 0.1)',
                        type: 'dashed'
                    }
                }
            },
            yAxis: {
                type: 'value',
                min: 25,
                max: 50,
                axisLine: {
                    lineStyle: {
                        color: 'rgba(255, 112, 67, 0.3)'
                    }
                },
                axisLabel: {
                    color: '#b8c5d6',
                    fontSize: 10,
                    formatter: '{value}°C'
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(255, 112, 67, 0.1)',
                        type: 'dashed'
                    }
                }
            },
            series: [{
                name: '温度',
                type: 'line',
                data: this.chartData.temperature,
                smooth: true,
                symbol: 'circle',
                symbolSize: 4,
                lineStyle: {
                    color: '#ff7043',
                    width: 2
                },
                itemStyle: {
                    color: '#ff7043',
                    borderColor: '#ffffff',
                    borderWidth: 1
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: 'rgba(255, 112, 67, 0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(255, 112, 67, 0.05)'
                        }]
                    }
                },
                markLine: {
                    silent: true,
                    lineStyle: {
                        color: '#ffaa00',
                        type: 'dashed',
                        width: 1
                    },
                    data: [{
                        yAxis: 40,
                        name: '警告线'
                    }]
                },
                emphasis: {
                    itemStyle: {
                        color: '#ff8a65',
                        borderColor: '#ffffff',
                        borderWidth: 2,
                        shadowBlur: 10,
                        shadowColor: 'rgba(255, 112, 67, 0.5)'
                    }
                }
            }],
            animation: true,
            animationDuration: 1000,
            animationEasing: 'cubicOut'
        };

        this.charts.temperature.setOption(option);
    }

    /**
     * 生成时间序列数据
     */
    generateTimeSeriesData(hours, min, max) {
        const data = [];
        const now = new Date();
        
        for (let i = hours - 1; i >= 0; i--) {
            const baseValue = min + (max - min) * 0.5;
            const variation = (max - min) * 0.3;
            const value = baseValue + (Math.random() - 0.5) * variation;
            data.push(Number(value.toFixed(1)));
        }
        
        return data;
    }

    /**
     * 生成时间标签
     */
    generateTimeLabels(hours) {
        const labels = [];
        const now = new Date();
        
        for (let i = hours - 1; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 60 * 60 * 1000);
            labels.push(time.getHours().toString().padStart(2, '0') + ':00');
        }
        
        return labels;
    }

    /**
     * 启动数据更新
     */
    startDataUpdate() {
        setInterval(() => {
            this.updateChartData();
        }, 5000); // 每5秒更新一次数据
    }

    /**
     * 更新图表数据
     */
    updateChartData() {
        // 更新电压数据
        if (this.charts.voltage) {
            const voltageData = this.chartData.voltage;
            voltageData.shift(); // 移除第一个数据点
            
            // 添加新的数据点
            const newVoltage = 215 + Math.random() * 10;
            voltageData.push(Number(newVoltage.toFixed(1)));
            
            // 更新时间标签
            const timeLabels = this.generateTimeLabels(24);
            
            this.charts.voltage.setOption({
                xAxis: {
                    data: timeLabels
                },
                series: [{
                    data: voltageData
                }]
            });
        }

        // 更新温度数据
        if (this.charts.temperature) {
            const temperatureData = this.chartData.temperature;
            temperatureData.shift(); // 移除第一个数据点
            
            // 添加新的数据点
            const newTemperature = 30 + Math.random() * 15;
            temperatureData.push(Number(newTemperature.toFixed(1)));
            
            // 更新时间标签
            const timeLabels = this.generateTimeLabels(24);
            
            this.charts.temperature.setOption({
                xAxis: {
                    data: timeLabels
                },
                series: [{
                    data: temperatureData
                }]
            });
        }
    }

    /**
     * 根据时间范围更新图表
     */
    updateChartTimeRange(chartType, timeRange) {
        let hours;
        switch (timeRange) {
            case '1H':
                hours = 1;
                break;
            case '6H':
                hours = 6;
                break;
            case '24H':
                hours = 24;
                break;
            default:
                hours = 24;
        }

        const newData = this.generateTimeSeriesData(hours, 
            chartType === 'voltage' ? 215 : 30,
            chartType === 'voltage' ? 225 : 45
        );
        const newLabels = this.generateTimeLabels(hours);

        if (this.charts[chartType]) {
            this.charts[chartType].setOption({
                xAxis: {
                    data: newLabels
                },
                series: [{
                    data: newData
                }]
            });
        }
    }

    /**
     * 调整图表大小
     */
    resizeCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.resize) {
                chart.resize();
            }
        });
    }

    /**
     * 销毁所有图表
     */
    dispose() {
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.dispose) {
                chart.dispose();
            }
        });
        this.charts = {};
    }

    /**
     * 获取图表实例
     */
    getChart(chartType) {
        return this.charts[chartType];
    }

    /**
     * 设置图表主题
     */
    setTheme(theme) {
        // 可以根据需要切换图表主题
        Object.keys(this.charts).forEach(chartType => {
            if (this.charts[chartType]) {
                // 重新初始化图表以应用新主题
                this.charts[chartType].dispose();
                this.initChart(chartType, theme);
            }
        });
    }
}

// 扩展DigitalTwinSystem类以集成图表功能
if (window.DigitalTwinSystem) {
    const originalInit = window.DigitalTwinSystem.prototype.initializeCharts;
    
    window.DigitalTwinSystem.prototype.initializeCharts = function() {
        this.chartManager = new ChartManager();
        this.charts = this.chartManager.charts;
        console.log('图表管理器初始化完成');
    };

    window.DigitalTwinSystem.prototype.updateChartData = function(container, timeRange) {
        const chartId = container.querySelector('.chart').id;
        let chartType;
        
        if (chartId === 'voltageChart') {
            chartType = 'voltage';
        } else if (chartId === 'temperatureChart') {
            chartType = 'temperature';
        }
        
        if (chartType && this.chartManager) {
            this.chartManager.updateChartTimeRange(chartType, timeRange);
        }
    };

    window.DigitalTwinSystem.prototype.handleResize = function() {
        if (this.chartManager) {
            this.chartManager.resizeCharts();
        }
    };
}

// 导出图表管理器类
window.ChartManager = ChartManager;
