<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统参数页面演示 - 桂林智源 SVG 数字化系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 30px;
        }

        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid rgba(0, 212, 255, 0.4);
        }

        .demo-header h1 {
            font-size: 32px;
            color: #00d4ff;
            text-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
            margin-bottom: 10px;
        }

        .demo-header p {
            font-size: 16px;
            color: #7a8ba0;
            margin: 0;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(42, 49, 66, 0.7);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            padding: 20px;
        }

        .feature-card h3 {
            color: #00d4ff;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .feature-card ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-card li {
            padding: 5px 0;
            color: #ffffff;
            font-size: 14px;
        }

        .feature-card li::before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }

        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #7a8ba0;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .demo-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
        }

        .demo-button:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.6);
            transform: translateY(-2px);
        }

        .demo-button.secondary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
        }

        .demo-button.secondary:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.6);
        }

        .parameter-preview {
            background: rgba(42, 49, 66, 0.7);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
        }

        .parameter-preview h3 {
            color: #00d4ff;
            margin-bottom: 15px;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        .param-table th,
        .param-table td {
            border: 1px solid rgba(0, 212, 255, 0.2);
            padding: 8px;
            text-align: left;
        }

        .param-table th {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            font-weight: bold;
        }

        .param-table td {
            background: rgba(42, 49, 66, 0.5);
            color: #ffffff;
        }

        .param-table tr:hover td {
            background: rgba(42, 49, 66, 0.8);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>系统参数配置页面</h1>
            <p>基于控制模式页面模板创建的62个系统参数管理界面</p>
        </div>

        <div class="stats-section">
            <div class="stat-card">
                <div class="stat-number">62</div>
                <div class="stat-label">系统参数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4</div>
                <div class="stat-label">每行参数数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">16</div>
                <div class="stat-label">表格行数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">2</div>
                <div class="stat-label">小数位数</div>
            </div>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>🎛️ 参数输入控件</h3>
                <ul>
                    <li>浮点数输入框（HTML input type="number"）</li>
                    <li>数值范围：-99999.00 到 99999.00</li>
                    <li>小数位数：固定保留2位小数</li>
                    <li>输入验证：确保输入值在有效范围内</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>📊 表格布局</h3>
                <ul>
                    <li>每行显示4个参数</li>
                    <li>共16行（最后一行显示2个参数）</li>
                    <li>包含序号、参数名称、设定值、当前值、单位</li>
                    <li>响应式设计适配大屏显示</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>🔄 实时数据</h3>
                <ul>
                    <li>MQTT连接获取实时数据</li>
                    <li>10秒更新间隔</li>
                    <li>首次同步设定值与当前值</li>
                    <li>连接状态实时显示</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>⚙️ 技术特性</h3>
                <ul>
                    <li>继承控制模式页面架构</li>
                    <li>使用通用参数配置样式</li>
                    <li>扩展SystemParameterManager类</li>
                    <li>保持工业监控界面风格</li>
                </ul>
            </div>
        </div>

        <div class="parameter-preview">
            <h3>📋 部分系统参数预览</h3>
            <table class="param-table">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>标识符</th>
                        <th>单位</th>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>标识符</th>
                        <th>单位</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>额定电流</td>
                        <td>SVG_1901</td>
                        <td>A</td>
                        <td>2</td>
                        <td>额定电压</td>
                        <td>SVG_1902</td>
                        <td>V</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>电流定标系数</td>
                        <td>SVG_1903</td>
                        <td>-</td>
                        <td>4</td>
                        <td>直流侧额定电压</td>
                        <td>SVG_1904</td>
                        <td>V</td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>电网频率</td>
                        <td>SVG_1905</td>
                        <td>Hz</td>
                        <td>6</td>
                        <td>单元载波频率</td>
                        <td>SVG_1906</td>
                        <td>Hz</td>
                    </tr>
                    <tr>
                        <td colspan="8" style="text-align: center; color: #7a8ba0; font-style: italic;">
                            ... 共62个系统参数 ...
                        </td>
                    </tr>
                    <tr>
                        <td>61</td>
                        <td>d 轴负向辅助校正系数</td>
                        <td>SVG_1964</td>
                        <td>-</td>
                        <td>62</td>
                        <td>q 轴负向辅助校正系数</td>
                        <td>SVG_1965</td>
                        <td>-</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="action-buttons">
            <a href="系统参数.html" class="demo-button">🚀 打开系统参数页面</a>
            <a href="控制模式.html" class="demo-button secondary">📋 查看控制模式页面</a>
        </div>
    </div>
</body>
</html>
