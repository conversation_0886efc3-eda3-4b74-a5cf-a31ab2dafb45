<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白云电气设备数字孪生系统 - Unity集成演示</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/animations.css">
    <!-- 引入 ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Unity集成专用样式 */
        .unity-demo-header {
            background: linear-gradient(135deg, #1a1f2e 0%, #2a3142 100%);
            border-bottom: 2px solid #00d4ff;
            padding: 10px 20px;
            margin-bottom: 10px;
        }
        
        .unity-demo-title {
            color: #00d4ff;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .unity-status {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-left: auto;
        }
        
        .unity-status-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #b8c5d6;
        }
        
        .unity-status-item.loaded {
            color: #00ff88;
        }
        
        .unity-controls {
            background: rgba(26, 31, 46, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .unity-controls h4 {
            color: #00d4ff;
            margin: 0 0 10px 0;
            font-size: 14px;
        }
        
        .unity-control-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .unity-control-btn {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(0, 153, 204, 0.2) 100%);
            border: 1px solid rgba(0, 212, 255, 0.5);
            color: #00d4ff;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .unity-control-btn:hover {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.3) 0%, rgba(0, 153, 204, 0.3) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }
        
        .unity-control-btn:active {
            transform: translateY(0);
        }
        
        .unity-control-btn.active {
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: #1a1f2e;
        }
        
        .unity-info-panel {
            background: rgba(26, 31, 46, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .unity-info-panel h4 {
            color: #00d4ff;
            margin: 0 0 10px 0;
            font-size: 14px;
        }
        
        .unity-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid rgba(184, 197, 214, 0.1);
            font-size: 12px;
        }
        
        .unity-info-item:last-child {
            border-bottom: none;
        }
        
        .unity-info-label {
            color: #b8c5d6;
        }
        
        .unity-info-value {
            color: #ffffff;
            font-weight: 500;
        }
        
        .unity-iframe-container {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid rgba(0, 212, 255, 0.3);
        }
        
        .unity-iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <!-- 主容器 -->
    <div id="app" class="app-container">
        <!-- Unity演示专用头部 -->
        <div class="unity-demo-header">
            <div class="unity-demo-title">
                <i class="fas fa-cube"></i>
                Unity WebGL 集成演示
            </div>
            <div class="unity-status">
                <div class="unity-status-item" id="unityStatusIndicator">
                    <i class="fas fa-circle"></i>
                    <span>Unity状态: 加载中</span>
                </div>
                <div class="unity-status-item">
                    <i class="fas fa-clock"></i>
                    <span id="currentTime"></span>
                </div>
            </div>
        </div>

        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-bolt"></i>
                    <span class="logo-text">白云电气</span>
                </div>
                <div class="system-title">设备数字孪生监控系统 - Unity集成版</div>
            </div>
            <div class="header-center">
                <div class="time-display" id="headerTime"></div>
            </div>
            <div class="header-right">
                <div class="status-indicators">
                    <div class="status-item online">
                        <i class="fas fa-circle"></i>
                        <span>系统在线</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-users"></i>
                        <span>3人在线</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧控制面板 -->
            <aside class="left-panel">
                <div class="panel-header">
                    <h3>Unity控制</h3>
                </div>
                
                <!-- Unity专用控制区域 -->
                <div class="unity-controls">
                    <h4>视角控制</h4>
                    <div class="unity-control-buttons">
                        <button class="unity-control-btn" id="overviewBtn">
                            <i class="fas fa-eye"></i>
                            总览视角
                        </button>
                        <button class="unity-control-btn" id="tourBtn">
                            <i class="fas fa-route"></i>
                            自动漫游
                        </button>
                        <button class="unity-control-btn" id="expandBtn">
                            <i class="fas fa-expand-arrows-alt"></i>
                            设备展开
                        </button>
                        <button class="unity-control-btn" id="resetBtn">
                            <i class="fas fa-home"></i>
                            重置视角
                        </button>
                    </div>
                </div>
                
                <div class="unity-controls">
                    <h4>场景控制</h4>
                    <div class="unity-control-buttons">
                        <button class="unity-control-btn" id="fullscreenBtn">
                            <i class="fas fa-expand"></i>
                            全屏显示
                        </button>
                        <button class="unity-control-btn" id="screenshotBtn">
                            <i class="fas fa-camera"></i>
                            截图
                        </button>
                        <button class="unity-control-btn" id="refreshBtn">
                            <i class="fas fa-sync-alt"></i>
                            刷新场景
                        </button>
                    </div>
                </div>
                
                <!-- Unity信息面板 -->
                <div class="unity-info-panel">
                    <h4>Unity信息</h4>
                    <div class="unity-info-item">
                        <span class="unity-info-label">加载状态:</span>
                        <span class="unity-info-value" id="unityLoadStatus">加载中...</span>
                    </div>
                    <div class="unity-info-item">
                        <span class="unity-info-label">版本:</span>
                        <span class="unity-info-value">2023.3.0f1</span>
                    </div>
                    <div class="unity-info-item">
                        <span class="unity-info-label">渲染模式:</span>
                        <span class="unity-info-value">WebGL 2.0</span>
                    </div>
                    <div class="unity-info-item">
                        <span class="unity-info-label">帧率:</span>
                        <span class="unity-info-value" id="unityFPS">-- FPS</span>
                    </div>
                </div>
                
                <div class="control-section">
                    <div class="control-group">
                        <h4>设备状态</h4>
                        <div class="device-status-list">
                            <div class="device-item">
                                <div class="device-name">主变压器</div>
                                <div class="device-status running">运行中</div>
                            </div>
                            <div class="device-item">
                                <div class="device-name">开关柜A</div>
                                <div class="device-status running">运行中</div>
                            </div>
                            <div class="device-item">
                                <div class="device-name">开关柜B</div>
                                <div class="device-status warning">告警</div>
                            </div>
                            <div class="device-item">
                                <div class="device-name">保护装置</div>
                                <div class="device-status running">运行中</div>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 中央3D展示区域 -->
            <section class="center-panel">
                <div class="unity-container" id="unityContainer">
                    <!-- Unity WebGL 内容将在这里加载 -->
                    <div class="unity-placeholder">
                        <div class="placeholder-content">
                            <i class="fas fa-cube rotating"></i>
                            <h3>Unity WebGL加载中...</h3>
                            <p>正在初始化真实的Unity 3D引擎</p>
                            <div class="loading-bar">
                                <div class="loading-progress" id="loadingProgress"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 3D场景控制工具栏 -->
                <div class="scene-toolbar">
                    <div class="toolbar-group">
                        <button class="toolbar-btn" title="重置视角" id="toolbarResetBtn">
                            <i class="fas fa-home"></i>
                        </button>
                        <button class="toolbar-btn" title="全屏显示" id="toolbarFullscreenBtn">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="toolbar-btn" title="截图" id="toolbarScreenshotBtn">
                            <i class="fas fa-camera"></i>
                        </button>
                        <button class="toolbar-btn" title="Unity控制台" id="toolbarConsoleBtn">
                            <i class="fas fa-terminal"></i>
                        </button>
                    </div>
                </div>
            </section>

            <!-- 右侧数据面板 -->
            <aside class="right-panel">
                <div class="panel-header">
                    <h3>实时监控</h3>
                </div>
                
                <!-- 关键指标卡片 -->
                <div class="metrics-cards">
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">220.5V</div>
                            <div class="metric-label">电压</div>
                        </div>
                        <div class="metric-trend up">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-thermometer-half"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">35.2°C</div>
                            <div class="metric-label">温度</div>
                        </div>
                        <div class="metric-trend stable">
                            <i class="fas fa-minus"></i>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">87%</div>
                            <div class="metric-label">负载率</div>
                        </div>
                        <div class="metric-trend down">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 实时数据图表区域 -->
                <div class="charts-section">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h4>电压趋势</h4>
                            <div class="chart-controls">
                                <button class="chart-btn active">1H</button>
                                <button class="chart-btn">6H</button>
                                <button class="chart-btn">24H</button>
                            </div>
                        </div>
                        <div class="chart" id="voltageChart"></div>
                    </div>
                    
                    <div class="chart-container">
                        <div class="chart-header">
                            <h4>温度监测</h4>
                        </div>
                        <div class="chart" id="temperatureChart"></div>
                    </div>
                </div>
            </aside>
        </main>

        <!-- 底部状态栏 -->
        <footer class="footer">
            <div class="footer-left">
                <div class="system-info">
                    <span>Unity集成版本: v2.1.0</span>
                    <span>|</span>
                    <span>最后更新: 2024-01-15 14:30:25</span>
                </div>
            </div>
            <div class="footer-center">
                <div class="connection-status">
                    <i class="fas fa-wifi"></i>
                    <span>连接状态: 正常</span>
                    <div class="signal-strength">
                        <div class="signal-bar active"></div>
                        <div class="signal-bar active"></div>
                        <div class="signal-bar active"></div>
                        <div class="signal-bar"></div>
                    </div>
                </div>
            </div>
            <div class="footer-right">
                <div class="copyright">
                    © 2024 白云电气集团 - Unity WebGL集成演示
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript 文件 -->
    <script src="scripts/main.js"></script>
    <script src="scripts/charts.js"></script>
    <script src="scripts/unity-integration.js"></script>
    <script src="scripts/unity-demo.js"></script>
</body>
</html>
