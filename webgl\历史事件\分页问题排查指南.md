# 分页问题排查指南

## 问题描述

点击下一页时数据没有显示，可能的原因和解决方案如下：

## 已修复的问题

### 1. 模拟数据分页问题
**问题**: 模拟数据生成器每次都生成相同的ID范围，导致分页时数据重复。

**修复**: 
- 根据当前页码生成不同的数据ID
- 确保每页的数据都是唯一的

```javascript
// 修复前
alertLogId: 135900 + i

// 修复后  
const startIndex = (currentPage - 1) * pageSize;
alertLogId: 135900 + startIndex + i
```

### 2. 添加调试信息
**新增功能**: 在关键函数中添加了详细的调试日志

## 排查步骤

### 1. 检查控制台日志
打开浏览器开发者工具（F12），切换到Console标签页，点击下一页时查看日志：

```
切换页码: {requestedPage: 2, currentPage: 1, totalPages: 20, totalCount: 1000, pageSize: 50}
开始加载第 2 页数据
GET请求URL: https://...?pageNum=2&pageSize=50...
处理API数据: {total: 1000, rowsCount: 50, currentPage: 2, pageSize: 50}
渲染事件列表: {filteredEventsCount: 50, currentPage: 2, totalCount: 1000}
```

### 2. 检查网络请求
在Network标签页中查看：
- 是否发送了新的API请求
- 请求的pageNum参数是否正确
- 响应数据是否正常

### 3. 检查数据处理
确认以下几点：
- `totalCount` 是否大于 `pageSize`
- `currentPage` 是否正确更新
- `filteredEvents` 是否包含新数据

## 可能的问题和解决方案

### 问题1: API请求失败
**症状**: 控制台显示网络错误或CORS错误

**解决方案**:
1. 检查网络连接
2. 确认API服务器状态
3. 使用模拟数据模式测试：
   ```javascript
   enableMockDataMode()
   ```

### 问题2: 认证token过期
**症状**: 返回401错误

**解决方案**:
1. 更新token
2. 重新登录获取新token

### 问题3: 参数格式错误
**症状**: 返回400错误或参数解析失败

**解决方案**:
1. 检查URL参数格式
2. 确认时间参数格式正确

### 问题4: 数据为空
**症状**: API返回成功但rows为空

**解决方案**:
1. 检查筛选条件是否过于严格
2. 调整时间范围
3. 检查数据库中是否有足够的数据

### 问题5: 前端渲染问题
**症状**: 数据获取成功但页面不显示

**解决方案**:
1. 检查DOM元素是否存在
2. 检查CSS样式是否正确
3. 检查JavaScript错误

## 调试命令

### 在浏览器控制台中执行以下命令进行调试：

```javascript
// 查看当前状态
console.log('当前页码:', currentPage);
console.log('总记录数:', totalCount);
console.log('每页数量:', pageSize);
console.log('总页数:', Math.ceil(totalCount / pageSize));
console.log('当前数据:', filteredEvents);

// 手动切换到第2页
changePage(2);

// 启用模拟数据模式
enableMockDataMode();

// 手动加载数据
loadHistoryEvents();

// 检查API配置
console.log('API配置:', API_CONFIG);

// 检查筛选条件
console.log('当前筛选:', currentFilter);
```

## 测试步骤

### 1. 模拟数据测试
1. 点击"使用模拟数据"按钮
2. 等待数据加载完成
3. 点击"下一页"按钮
4. 检查数据是否更新

### 2. 真实API测试
1. 确保CORS问题已解决
2. 确认认证token有效
3. 点击"下一页"按钮
4. 检查网络请求和响应

### 3. 边界条件测试
1. 测试第一页（上一页按钮应该禁用）
2. 测试最后一页（下一页按钮应该禁用）
3. 测试空数据情况

## 常见错误信息

### 1. "页码超出范围"
**原因**: 请求的页码超出了有效范围
**解决**: 检查totalCount和pageSize的计算

### 2. "CORS跨域请求被阻止"
**原因**: 服务器不允许跨域访问
**解决**: 使用模拟数据模式或配置CORS

### 3. "认证失败"
**原因**: token无效或过期
**解决**: 更新认证token

### 4. "加载数据失败"
**原因**: API请求失败
**解决**: 检查网络和服务器状态

## 性能优化建议

### 1. 缓存机制
考虑添加页面数据缓存，避免重复请求相同页面的数据。

### 2. 预加载
可以预加载下一页数据，提高用户体验。

### 3. 虚拟滚动
对于大量数据，考虑使用虚拟滚动替代分页。

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 浏览器控制台的完整错误日志
2. Network标签页的请求详情
3. 当前使用的是模拟数据还是真实API
4. 具体的操作步骤

通过以上排查步骤，应该能够定位和解决分页显示问题。
