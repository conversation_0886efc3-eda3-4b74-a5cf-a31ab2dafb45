# MQTT 电气系统实时数据更新功能

## 功能概述

本功能基于现有的 MQTT 工具配置，实现了电气系统实时数据的订阅、处理和界面更新。系统能够：

1. **自动连接 MQTT 服务器**：使用现有的连接配置和认证机制
2. **订阅电气系统数据**：监听主题 `/189/D19QBHKRZ791U/function/get`
3. **智能数据处理**：解析、验证和转换接收到的 JSON 数据
4. **实时界面更新**：更新电气系统相关的显示参数和状态指示器
5. **错误处理和重连**：确保连接稳定性和数据准确性

## 文件结构

```
webgl/
├── main.html                    # 主界面文件（已更新）
├── mqttTool.js                  # MQTT 工具库（现有）
├── electricalDataProcessor.js   # 电气数据处理器（新增）
├── mqtt-test.html              # MQTT 功能测试页面（新增）
├── 物模型/
│   └── 电气拓扑.json            # 物模型数据格式（现有）
└── MQTT-README.md              # 本说明文档
```

## 核心组件

### 1. MQTTElectricalDataManager 类

位于 `main.html` 中的核心管理类，负责：

- MQTT 连接管理
- 数据订阅和消息处理
- 界面更新协调
- 错误处理和重连机制

### 2. ElectricalDataProcessor 类

位于 `electricalDataProcessor.js` 中的数据处理类，负责：

- JSON 数据解析和验证
- 物模型数据格式检查
- 数据完整性验证
- 数据质量评估

### 3. 界面更新系统

集成到现有界面中的更新机制：

- 状态指示器更新
- 参数数值显示
- 连接状态监控
- 数据质量指示

## 数据格式

### 实际 MQTT 数据格式

系统支持实际接收到的 MQTT 数据格式：

```json
{
  "message": [
    {"id":"HMI_30039_5","name":"运行","ts":"2025-07-23 15:09:56.058","value":"1"},
    {"id":"HMI_30039_6","name":"故障","ts":"2025-07-23 15:09:56.058","value":"0"},
    {"id":"HMI_30039_4","name":"就绪","ts":"2025-07-23 15:09:56.058","value":"1"},
    {"id":"HMI_32030","name":"母线电压Uab","ts":"2025-07-23 15:09:56.058","value":"10.5"},
    {"id":"HMI_32032","name":"母线电压Ubc","ts":"2025-07-23 15:09:56.058","value":"10.4"},
    {"id":"HMI_32050","name":"功率因数","ts":"2025-07-23 15:09:56.058","value":"0.95"},
    {"id":"HMI_32048","name":"负载无功功率","ts":"2025-07-23 15:09:56.058","value":"-2.5"}
  ],
  "sources": "[{\"id\":\"HMI_30039_5\",\"value\":1},{\"id\":\"HMI_30039_6\",\"value\":0}]"
}
```

### 支持的参数类型

#### 状态参数（0/1 值）
- **HMI_30039_0**: 起始状态
- **HMI_30039_1**: 充电
- **HMI_30039_2**: 单元自检
- **HMI_30039_3**: 复位
- **HMI_30039_4**: 就绪
- **HMI_30039_5**: 运行
- **HMI_30039_6**: 故障
- **HMI_30039_7**: 高压
- **HMI_30039_8**: 开机自检
- **HMI_30039_9**: 备用
- **HMI_30039_10**: 合高压等待
- **HMI_30039_11**: 故障2
- **HMI_30039_12**: 复位2

#### 电压参数（kV）
- **HMI_32030**: 母线电压Uab
- **HMI_32032**: 母线电压Ubc
- **HMI_32034**: 母线电压Uca

#### 电流参数（A）
- **HMI_32040**: SVG电流la
- **HMI_32042**: SVG电流lb
- **HMI_32044**: SVG电流lc
- **HMI_32046**: 网侧负载无功电流

#### 功率参数
- **HMI_32048**: 负载无功功率（MVAr）
- **HMI_32050**: 功率因数

### 兼容的旧格式

系统同时支持旧的属性格式：

```json
{
  "properties": {
    "HMI_30039_5": 1,   // 运行
    "HMI_30039_6": 0,   // 故障
    "HMI_32050": 0.95   // 功率因数
  },
  "timestamp": "2024-01-15T14:30:25.000Z",
  "deviceId": "D19QBHKRZ791U"
}
```

### 处理后数据格式

```json
{
  "timestamp": "2024-01-15T14:30:25.000Z",
  "deviceId": "D19QBHKRZ791U",
  "properties": {
    "HMI_30039_5": {
      "id": "HMI_30039_5",
      "name": "运行",
      "value": 1,
      "unit": "",
      "type": "integer",
      "isValid": true,
      "processedAt": "2024-01-15T14:30:25.100Z"
    }
    // ... 其他属性
  }
}
```

## 使用方法

### 1. 自动初始化

系统会在页面加载时自动初始化：

```javascript
// 页面加载完成后自动执行
document.addEventListener('DOMContentLoaded', initializePage);
```

### 2. 手动控制

可以通过以下函数进行手动控制：

```javascript
// 获取连接状态
const status = getMQTTConnectionStatus();

// 手动重连
await reconnectMQTT();

// 获取数据处理统计
const stats = mqttElectricalManager.getDataProcessingStatistics();
```

### 3. 添加数据更新回调

```javascript
mqttElectricalManager.addDataUpdateCallback((data, metadata) => {
  console.log('收到新数据:', data);
  console.log('数据质量:', metadata.dataQuality);
});
```

## 界面集成

### 1. 状态指示器

在页面头部添加了以下状态指示器：

- **系统状态**：显示整体系统运行状态
- **MQTT 连接状态**：显示连接状态
- **数据质量指示器**：显示数据质量百分比

### 2. 参数计数器

在电气系统状态区域添加了状态计数器：

- 运行状态计数
- 故障状态计数
- 就绪状态计数
- 备用状态计数

### 3. 实时参数更新

现有的电气参数显示会根据 MQTT 数据实时更新，包括：

- 负载无功功率
- 功率因数
- 网侧负载无功电流
- 母线电压等

## 测试和调试

### 1. 使用测试页面

#### 完整功能测试 (`mqtt-test.html`)
1. 点击"连接 MQTT"按钮连接到实际的 MQTT 服务器
2. 点击"发送测试数据"模拟数据接收
3. 点击"运行数据处理测试"验证数据处理功能
4. 观察数据处理结果和界面更新

#### 快速验证测试 (`quick-test.html`)
1. 打开页面自动加载测试环境
2. 点击"运行快速测试"验证数据处理功能
3. 查看详细的测试结果和数据质量评估

#### 数据处理测试 (`test-data-processing.js`)
在浏览器控制台中运行：
```javascript
// 测试数据处理功能
testDataProcessing()

// 测试多种数据格式
testMultipleFormats()

// 性能测试
performanceTest()

// 运行所有测试
runAllTests()
```

### 2. 控制台调试

在浏览器控制台中可以查看详细日志：

```javascript
// 查看连接状态
console.log(getMQTTConnectionStatus());

// 查看数据处理统计
console.log(mqttElectricalManager.getDataProcessingStatistics());

// 查看支持的参数列表
console.log(mqttElectricalManager.getSupportedParameters());
```

### 3. 数据质量监控

系统会自动评估数据质量：

- **Excellent (95%+)**：数据完整且准确
- **Good (80-95%)**：数据基本完整
- **Fair (60-80%)**：数据部分缺失
- **Poor (<60%)**：数据质量较差

## 配置选项

### 1. MQTT 连接配置

在 `MQTTElectricalDataManager` 构造函数中可以修改：

```javascript
this.subscriptionTopic = '/189/D19QBHKRZ791U/ws/service';  // 实际订阅主题
this.maxReconnectAttempts = 5;
this.reconnectInterval = 5000; // 5秒
```

实际的 MQTT 连接配置：
- **服务器地址**: `wss://mqtt.qizhiyun.cc/mqtt`
- **用户名**: `FastBee`
- **密码**: JWT Token（从认证系统获取）
- **订阅主题**: `/189/D19QBHKRZ791U/ws/service`

### 2. 数据验证规则

在 `ElectricalDataProcessor` 中可以修改数据模式：

```javascript
this.dataSchema = {
  properties: {
    'HMI_30039_5': { name: '运行', type: 'integer', min: 0, max: 100, unit: '' }
    // ... 添加更多参数
  }
};
```

## 错误处理

系统包含完善的错误处理机制：

1. **连接错误**：自动重连，最多尝试 5 次
2. **数据格式错误**：记录错误日志，跳过无效数据
3. **处理异常**：捕获异常，不影响其他功能
4. **网络中断**：自动检测并尝试重连

## 性能优化

1. **批量更新**：避免频繁的 DOM 操作
2. **数据缓存**：缓存最近的数据状态
3. **智能更新**：只更新变化的参数
4. **资源清理**：页面卸载时自动清理资源

## 扩展性

系统设计具有良好的扩展性：

1. **新增参数**：在数据处理器中添加新的参数定义
2. **自定义处理**：添加数据更新回调函数
3. **界面扩展**：添加新的状态指示器和显示元素
4. **多设备支持**：可以扩展支持多个设备的数据订阅

## 注意事项

1. **网络环境**：确保能够访问 MQTT 服务器
2. **认证配置**：确保 token 或认证信息正确
3. **浏览器兼容性**：建议使用现代浏览器
4. **数据格式**：确保 MQTT 数据符合预期格式
5. **性能监控**：定期检查数据处理统计信息

## 故障排除

### 常见问题

1. **连接失败**
   - 检查 MQTT 服务器地址和端口
   - 验证认证信息
   - 检查网络连接

2. **数据不更新**
   - 检查订阅主题是否正确
   - 验证数据格式是否符合物模型
   - 查看控制台错误日志

3. **界面显示异常**
   - 检查 DOM 元素 ID 是否正确
   - 验证 CSS 样式是否加载
   - 查看浏览器兼容性

### 调试步骤

1. 打开浏览器开发者工具
2. 查看控制台日志
3. 检查网络请求
4. 使用测试页面验证功能
5. 查看数据处理统计信息
