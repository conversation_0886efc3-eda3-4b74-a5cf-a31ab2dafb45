{"modbusConfiguration": {"description": "白云电气设备MODBUS数据点配置", "version": "1.0.0", "lastUpdated": "2024-01-15", "devices": [{"deviceId": "TRANSFORMER_01", "deviceName": "主变压器", "deviceType": "transformer", "slaveId": 1, "ipAddress": "*************", "port": 502, "dataPoints": [{"pointId": "VOLTAGE_A", "pointName": "A相电压", "address": 40001, "dataType": "float32", "unit": "V", "scaleFactor": 0.1, "minValue": 200, "maxValue": 240, "alarmThreshold": {"high": 230, "low": 210}, "updateInterval": 1000}, {"pointId": "VOLTAGE_B", "pointName": "B相电压", "address": 40003, "dataType": "float32", "unit": "V", "scaleFactor": 0.1, "minValue": 200, "maxValue": 240, "alarmThreshold": {"high": 230, "low": 210}, "updateInterval": 1000}, {"pointId": "VOLTAGE_C", "pointName": "C相电压", "address": 40005, "dataType": "float32", "unit": "V", "scaleFactor": 0.1, "minValue": 200, "maxValue": 240, "alarmThreshold": {"high": 230, "low": 210}, "updateInterval": 1000}, {"pointId": "CURRENT_A", "pointName": "A相电流", "address": 40007, "dataType": "float32", "unit": "A", "scaleFactor": 0.01, "minValue": 0, "maxValue": 1000, "alarmThreshold": {"high": 800, "low": 0}, "updateInterval": 1000}, {"pointId": "TEMPERATURE", "pointName": "变压器温度", "address": 40009, "dataType": "int16", "unit": "°C", "scaleFactor": 0.1, "minValue": -20, "maxValue": 80, "alarmThreshold": {"high": 60, "low": -10}, "updateInterval": 2000}, {"pointId": "LOAD_RATE", "pointName": "负载率", "address": 40011, "dataType": "int16", "unit": "%", "scaleFactor": 0.1, "minValue": 0, "maxValue": 100, "alarmThreshold": {"high": 90, "low": 0}, "updateInterval": 2000}]}, {"deviceId": "SWITCH_A", "deviceName": "开关柜A", "deviceType": "switchgear", "slaveId": 2, "ipAddress": "*************", "port": 502, "dataPoints": [{"pointId": "SWITCH_STATUS", "pointName": "开关状态", "address": 10001, "dataType": "bool", "unit": "", "description": "0=断开, 1=闭合", "updateInterval": 500}, {"pointId": "VOLTAGE", "pointName": "母线电压", "address": 40001, "dataType": "float32", "unit": "V", "scaleFactor": 0.1, "minValue": 200, "maxValue": 240, "alarmThreshold": {"high": 230, "low": 210}, "updateInterval": 1000}, {"pointId": "CURRENT", "pointName": "负载电流", "address": 40003, "dataType": "float32", "unit": "A", "scaleFactor": 0.01, "minValue": 0, "maxValue": 500, "alarmThreshold": {"high": 400, "low": 0}, "updateInterval": 1000}, {"pointId": "TEMPERATURE", "pointName": "柜体温度", "address": 40005, "dataType": "int16", "unit": "°C", "scaleFactor": 0.1, "minValue": -20, "maxValue": 60, "alarmThreshold": {"high": 45, "low": -10}, "updateInterval": 2000}]}, {"deviceId": "SWITCH_B", "deviceName": "开关柜B", "deviceType": "switchgear", "slaveId": 3, "ipAddress": "*************", "port": 502, "dataPoints": [{"pointId": "SWITCH_STATUS", "pointName": "开关状态", "address": 10001, "dataType": "bool", "unit": "", "description": "0=断开, 1=闭合", "updateInterval": 500}, {"pointId": "VOLTAGE", "pointName": "母线电压", "address": 40001, "dataType": "float32", "unit": "V", "scaleFactor": 0.1, "minValue": 200, "maxValue": 240, "alarmThreshold": {"high": 230, "low": 210}, "updateInterval": 1000}, {"pointId": "CURRENT", "pointName": "负载电流", "address": 40003, "dataType": "float32", "unit": "A", "scaleFactor": 0.01, "minValue": 0, "maxValue": 500, "alarmThreshold": {"high": 400, "low": 0}, "updateInterval": 1000}, {"pointId": "TEMPERATURE", "pointName": "柜体温度", "address": 40005, "dataType": "int16", "unit": "°C", "scaleFactor": 0.1, "minValue": -20, "maxValue": 60, "alarmThreshold": {"high": 45, "low": -10}, "updateInterval": 2000}]}, {"deviceId": "PROTECTION_01", "deviceName": "保护装置", "deviceType": "protection", "slaveId": 4, "ipAddress": "*************", "port": 502, "dataPoints": [{"pointId": "SYSTEM_STATUS", "pointName": "系统状态", "address": 10001, "dataType": "int16", "unit": "", "description": "0=正常, 1=告警, 2=故障", "updateInterval": 500}, {"pointId": "FAULT_CODE", "pointName": "故障代码", "address": 40001, "dataType": "int16", "unit": "", "description": "故障类型编码", "updateInterval": 1000}, {"pointId": "OPERATION_COUNT", "pointName": "动作次数", "address": 40003, "dataType": "int32", "unit": "次", "description": "保护动作累计次数", "updateInterval": 5000}]}], "alarmConfiguration": {"enableAlarms": true, "alarmLevels": {"info": {"priority": 1, "color": "#00d4ff", "sound": false}, "warning": {"priority": 2, "color": "#ffaa00", "sound": true}, "error": {"priority": 3, "color": "#ff4444", "sound": true}, "critical": {"priority": 4, "color": "#ff0000", "sound": true}}, "alarmRetention": {"maxRecords": 1000, "retentionDays": 30}}, "dataLogging": {"enableLogging": true, "logInterval": 60000, "logRetention": {"realTimeData": 7, "historicalData": 365}, "compressionEnabled": true}, "communicationSettings": {"timeout": 5000, "retryCount": 3, "retryInterval": 1000, "connectionPoolSize": 10, "keepAliveInterval": 30000}}}