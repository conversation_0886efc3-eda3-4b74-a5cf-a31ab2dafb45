<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT 数据处理快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MQTT 数据处理快速测试</h1>
        <p>这个页面用于快速验证数据处理功能是否正常工作。</p>
        
        <button onclick="runQuickTest()">运行快速测试</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="test-results"></div>
    </div>

    <script src="electricalDataProcessor.js"></script>
    <script src="test-data-processing.js"></script>
    
    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            
            if (typeof message === 'object') {
                resultDiv.innerHTML = `<pre>${JSON.stringify(message, null, 2)}</pre>`;
            } else {
                resultDiv.textContent = message;
            }
            
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
        
        function runQuickTest() {
            clearResults();
            addResult('开始快速测试...', 'info');
            
            try {
                // 测试数据处理器是否可用
                if (typeof ElectricalDataProcessor === 'undefined') {
                    addResult('❌ ElectricalDataProcessor 未定义', 'error');
                    return;
                }
                
                addResult('✓ ElectricalDataProcessor 已加载', 'success');
                
                // 创建处理器实例
                const processor = new ElectricalDataProcessor();
                addResult('✓ 数据处理器实例创建成功', 'success');
                
                // 测试实际的 MQTT 数据
                const testData = {
                    "message": [
                        {"id":"HMI_30039_5","name":"运行","ts":"2025-07-23 15:09:56.058","value":"1"},
                        {"id":"HMI_30039_6","name":"故障","ts":"2025-07-23 15:09:56.058","value":"0"},
                        {"id":"HMI_30039_4","name":"就绪","ts":"2025-07-23 15:09:56.058","value":"1"},
                        {"id":"HMI_32050","name":"功率因数","ts":"2025-07-23 15:09:56.058","value":"0.95"},
                        {"id":"HMI_32030","name":"母线电压Uab","ts":"2025-07-23 15:09:56.058","value":"10.5"}
                    ]
                };
                
                addResult('测试数据:', 'info');
                addResult(testData);
                
                // 处理数据
                const result = processor.processData(testData);
                
                if (result.success) {
                    addResult('✓ 数据处理成功', 'success');
                    addResult('处理结果:', 'info');
                    addResult(result.data);
                    
                    addResult('数据质量评估:', 'info');
                    addResult(result.metadata.dataQuality);
                    
                    // 显示处理后的参数
                    addResult('处理后的参数列表:', 'info');
                    Object.keys(result.data.properties).forEach(id => {
                        const prop = result.data.properties[id];
                        addResult(`${prop.name} (${id}): ${prop.value} ${prop.unit} [${prop.isValid ? '有效' : '无效'}]`, 'success');
                    });
                    
                } else {
                    addResult('❌ 数据处理失败: ' + result.error, 'error');
                    if (result.details) {
                        addResult('错误详情:', 'error');
                        addResult(result.details);
                    }
                }
                
                // 显示统计信息
                const stats = processor.getStatistics();
                addResult('处理统计:', 'info');
                addResult(stats);
                
                addResult('✓ 快速测试完成', 'success');
                
            } catch (error) {
                addResult('❌ 测试过程中发生错误: ' + error.message, 'error');
                console.error('测试错误:', error);
            }
        }
        
        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            addResult('页面加载完成，点击"运行快速测试"按钮开始测试', 'info');
        });
    </script>
</body>
</html>
