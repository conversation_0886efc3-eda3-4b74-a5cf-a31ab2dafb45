@echo off
echo ========================================
echo 白云电气数字孪生系统 - 高保真原型
echo Unity WebGL集成演示服务器
echo ========================================
echo.

echo 正在启动本地服务器...
echo 服务器地址: http://localhost:8000
echo.
echo 可用页面:
echo - 模拟版本: http://localhost:8000/index.html
echo - Unity集成版: http://localhost:8000/unity-demo.html
echo - 基础演示: http://localhost:8000/demo.html
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================
echo.

REM 尝试使用Python启动服务器
python -m http.server 8000 2>nul
if %errorlevel% neq 0 (
    echo Python未找到，尝试使用Node.js...
    npx serve . -p 8000 2>nul
    if %errorlevel% neq 0 (
        echo.
        echo 错误: 未找到Python或Node.js
        echo 请安装Python或Node.js后重试
        echo.
        echo Python安装: https://python.org
        echo Node.js安装: https://nodejs.org
        echo.
        pause
    )
)
