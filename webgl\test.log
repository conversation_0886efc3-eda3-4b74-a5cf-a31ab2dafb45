收到保护使能数据 (#83):   
{
    "message": [
        {
            "id": "2101_GOA1",
            "name": "(电网线电压有效值 Ⅰ 段过压报警使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_GOP2",
            "name": "(电网线电压有效值 Ⅱ 段过压保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_GOP3",
            "name": "(电网线电压幅值 Ⅲ 段过压保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_GIOP",
            "name": "(电网线电压瞬时值过压保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_GUA1",
            "name": "(电网线电压有效值 Ⅰ 段欠压报警使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_GUP2",
            "name": "(电网线电压有效值 Ⅱ 段欠压保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_GUP3",
            "name": "(电网线电压幅值 Ⅲ 段欠压保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_GVIP",
            "name": "(电网线电压有效值不平衡保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_GVPLP",
            "name": "(电网线电压缺相保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_GVRSP",
            "name": "(电网电压反序保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_SSLP",
            "name": "(同步信号丢失保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_SHSFP",
            "name": "(SVG 侧霍尔传感器故障保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_ABSSCFP",
            "name": "(模拟板采样通道自检故障保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_ZSVOP",
            "name": "(零序电压超标保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_SOCOA1",
            "name": "(SVG 输出电流有效值 Ⅰ 段过流报警使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_SOCOP2",
            "name": "(SVG 输出电流有效值 Ⅱ 段过流保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_SOCIOP",
            "name": "(SVG 输出电流瞬时值过流保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_SOCHOP",
            "name": "(SVG 输出电流硬件过流 (HW) 保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_SOCPLP",
            "name": "(SVG 输出电流缺相保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_SOCCLA",
            "name": "(SVG 输出电流指令限幅报警使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_SICOCDFP",
            "name": "(SVG 瞬时电流过流（CT 检测方式）故障保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_PTFP",
            "name": "(PT 故障保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_SZSCFP",
            "name": "(SVG 零序电流故障保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_PUGF",
            "name": "(功率单元状态一般性故障 (SW) 使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_PUUOP",
            "name": "(功率单元 UDC 过压保护 (SW) 使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_PUUIP",
            "name": "(功率单元 UDC 不平衡保护 (SW) 使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_PUHP",
            "name": "(功率单元硬件保护 (HW) 使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_PUSCFP",
            "name": "(功率单元自检故障保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_PUSIP",
            "name": "(功率单元状态不一致保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_RS485CTF",
            "name": "(RS485 通信超时故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2101_RS485CCF",
            "name": "(RS485 通信校验故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2101_DRTF",
            "name": "(DRAM 读超时故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_DF",
            "name": "(DRAM 故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_DWPF",
            "name": "(DRAM 写参数故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_WWRP",
            "name": "(WDI 看门狗复位保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_CTF",
            "name": "(充电超时故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_TSNCSDF",
            "name": "(行程开关未合故障使能 / 烟感故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_CCK1NE",
            "name": "(充电接触器 K1 不吸合使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_CCK1ND",
            "name": "(充电接触器 K1 不分开使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_CBQF1NE",
            "name": "(断路器 QF1 不吸合使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_CBQF1ND",
            "name": "(断路器 QF1 不分开使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_CCST",
            "name": "(CAN 通信发送超时使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_CCRT",
            "name": "(CAN 通信接收超时使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_WCSPF",
            "name": "(水冷系统电源故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_RFPE",
            "name": "(读铁电参数错误使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_TOA",
            "name": "(变压器超温报警使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_TOT",
            "name": "(变压器超温跳闸使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_TLGA",
            "name": "(变压器轻瓦斯报警使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_THGT",
            "name": "(变压器重瓦斯跳闸使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_TPA",
            "name": "(变压器压力报警使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_TPT",
            "name": "(变压器压力跳闸使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_FPLP",
            "name": "(风机缺相保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_USCF",
            "name": "(单元短路故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_IOC2T",
            "name": "(瞬时过流 2 跳闸使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_GLV1P",
            "name": "(电网低电压 1 保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_GLV2P",
            "name": "(电网低电压 2 保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_ARFCLP",
            "name": "(自动恢复失败次数超限保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_FRTP",
            "name": "(故障复位超时保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_ICFBP",
            "name": "(柜间光纤断保护使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_WCSOSA",
            "name": "(水冷系统运行状态异常使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_WCSCF",
            "name": "(水冷系统综合故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_WCSA",
            "name": "(水冷系统报警使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        },
        {
            "id": "2102_WCSRS",
            "name": "(水冷系统请求停止使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "1"
        },
        {
            "id": "2102_HSFCF",
            "name": "(高速光纤通信故障使能) 保护功能使能",
            "ts": "2025-09-09 15:15:32.474",
            "value": "0"
        }
    ],
    "sources": "[{\"id\": \"SVG_1001\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"5.489999771118164\", \"writeValue\": \"5.489999771118164\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1002\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2.799999952316284\", \"writeValue\": \"2.799999952316284\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1003\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"8.100000381469727\", \"writeValue\": \"8.100000381469727\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1004\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"15.5\", \"writeValue\": \"15.5\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1005\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"14.0\", \"writeValue\": \"14.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1006\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"15.0\", \"writeValue\": \"15.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1007\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.20000000298023224\", \"writeValue\": \"0.20000000298023224\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1008\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.123456954956055\", \"writeValue\": \"6.123456954956055\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1009\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"7.777777671813965\", \"writeValue\": \"7.777777671813965\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1010\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1101\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1102\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1103\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1104\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1105\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1201\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1202\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1203\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1204\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1205\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1206\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1207\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1208\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1209\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1210\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1301\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1302\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1303\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1304\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1305\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1306\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1307\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1308\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1309\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1310\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1401\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1402\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1403\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1404\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1405\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1406\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1407\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1408\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1409\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1410\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1501\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1502\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1503\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1504\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1505\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1506\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1507\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1508\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1509\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1510\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1601\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1602\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_1603\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_1604\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_1605\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_1606\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_1607\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1701\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1702\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1703\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1704\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1705\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1706\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1707\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1708\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1709\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1710\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1711\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1712\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1713\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1714\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1715\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1716\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1717\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1718\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1801\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1802\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1803\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1804\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1805\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1806\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1807\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1808\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1809\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1810\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1811\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1812\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1813\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1814\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1815\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1816\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1817\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1818\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1901\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"150.0\", \"writeValue\": \"150.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1902\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"380.0\", \"writeValue\": \"380.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1903\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"200.0\", \"writeValue\": \"200.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1904\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"900.0\", \"writeValue\": \"900.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1905\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"50.0\", \"writeValue\": \"50.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1906\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"500.0\", \"writeValue\": \"500.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1907\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2.0\", \"writeValue\": \"2.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1908\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1909\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0010416667209938169\", \"writeValue\": \"0.0010416667209938169\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1910\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.5\", \"writeValue\": \"0.5\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1911\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.5\", \"writeValue\": \"0.5\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1912\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.100000023841858\", \"writeValue\": \"1.100000023841858\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1913\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0010416667209938169\", \"writeValue\": \"0.0010416667209938169\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1914\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"805584928\", \"writeValue\": \"805584928\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_1915\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1916\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1917\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1918\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1919\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1920\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.05000000074505806\", \"writeValue\": \"0.05000000074505806\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1921\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.05000000074505806\", \"writeValue\": \"0.05000000074505806\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1922\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"50.0\", \"writeValue\": \"50.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1923\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0037499999161809683\", \"writeValue\": \"0.0037499999161809683\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1924\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1925\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1926\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1927\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1928\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1929\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1930\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1931\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1932\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1933\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1934\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1935\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1936\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1937\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1938\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1939\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1940\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1941\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1942\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1943\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1944\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1945\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1946\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1947\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1948\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1949\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1950\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.20000000298023224\", \"writeValue\": \"0.20000000298023224\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1951\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0005000000237487257\", \"writeValue\": \"0.0005000000237487257\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1952\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1953\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1954\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1955\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1956\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1957\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1958\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.009999999776482582\", \"writeValue\": \"0.009999999776482582\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1959\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0020000000949949026\", \"writeValue\": \"0.0020000000949949026\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1960\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_1961\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_1962\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1963\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1964\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_1965\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2001\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2002\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2003\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"3.0\", \"writeValue\": \"3.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2004\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"4\", \"writeValue\": \"4\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2005\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"5.0\", \"writeValue\": \"5.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2006\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6\", \"writeValue\": \"6\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2007\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"7.0\", \"writeValue\": \"7.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2008\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"9.0\", \"writeValue\": \"9.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2009\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"8\", \"writeValue\": \"8\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2010\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"11.0\", \"writeValue\": \"11.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2011\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"10\", \"writeValue\": \"10\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2012\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"13.0\", \"writeValue\": \"13.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2013\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"12\", \"writeValue\": \"12\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2014\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"15.0\", \"writeValue\": \"15.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2015\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"14\", \"writeValue\": \"14\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2016\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"17.0\", \"writeValue\": \"17.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2017\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"16\", \"writeValue\": \"16\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2018\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2019\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1.0\", \"writeValue\": \"1.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2020\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2021\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"3.0\", \"writeValue\": \"3.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2022\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"4\", \"writeValue\": \"4\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2023\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"5.0\", \"writeValue\": \"5.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2024\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2025\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"7\", \"writeValue\": \"7\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2026\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"4\", \"writeValue\": \"4\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2027\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"5\", \"writeValue\": \"5\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2028\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"8.0\", \"writeValue\": \"8.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2029\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"9\", \"writeValue\": \"9\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2030\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"3\", \"writeValue\": \"3\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2031\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2032\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"6.0\", \"writeValue\": \"6.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2033\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"10.0\", \"writeValue\": \"10.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2034\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"11\", \"writeValue\": \"11\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2035\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"12.0\", \"writeValue\": \"12.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2036\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"13\", \"writeValue\": \"13\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2101\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"3758096383\", \"writeValue\": \"3758096383\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2102\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1431655765\", \"writeValue\": \"1431655765\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2201\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"16843009\", \"writeValue\": \"16843009\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2202\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"28\", \"writeValue\": \"28\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2203\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"16843009\", \"writeValue\": \"16843009\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2301\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"33686018\", \"writeValue\": \"33686018\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2302\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"33686018\", \"writeValue\": \"33686018\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2303\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"Uint_32bit\"}, {\"id\": \"SVG_2304\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2305\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2306\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2307\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2308\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2309\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2310\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2311\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2312\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2313\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2314\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2315\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2316\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2317\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2318\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2319\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2320\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2321\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2322\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2323\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2324\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2325\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2326\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2327\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2328\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2329\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2330\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2331\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2332\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2333\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2334\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2335\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2401\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2402\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2403\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2404\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2405\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2406\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2407\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2408\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2409\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2410\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2501\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2502\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.10000000149011612\", \"writeValue\": \"0.10000000149011612\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2503\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.10000000149011612\", \"writeValue\": \"0.10000000149011612\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2504\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.10000000149011612\", \"writeValue\": \"0.10000000149011612\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2505\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2506\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2507\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2508\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2509\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"SVG_2510\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0.0\", \"writeValue\": \"0.0\", \"dataType\": \"Float_32bit\"}, {\"id\": \"1914_RPC\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_CM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_UL\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"8\", \"writeValue\": \"8\", \"dataType\": \"\"}, {\"id\": \"1914_PN\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_SCM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_BCE\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_GVFF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_NLME\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_PM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_LVRSE\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_CSM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"1914_MSM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_NLM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_BTM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_RCT\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"1914_LVRT\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_FRCE\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_TM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_CA\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_HCAM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_BCVC\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_OM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_ARSM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1914_VCT\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"1914_DCSM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"1914_NSCS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1960_RPOE\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1960_VPOE\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1960_RSV\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1961_VCP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"1961_LRFDM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"1961_RSV\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2101_GOA1\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_GOP2\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_GOP3\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_GIOP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_GUA1\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_GUP2\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_GUP3\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_GVIP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_GVPLP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_GVRSP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_SSLP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_SHSFP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_ABSSCFP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_ZSVOP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_SOCOA1\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_SOCOP2\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_SOCIOP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_SOCHOP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_SOCPLP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_SOCCLA\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_SICOCDFP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_PTFP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_SZSCFP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_PUGF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_PUUOP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_PUUIP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_PUHP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_PUSCFP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_PUSIP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_RS485CTF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2101_RS485CCF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2101_DRTF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_DF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_DWPF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_WWRP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_CTF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_TSNCSDF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_CCK1NE\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_CCK1ND\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_CBQF1NE\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_CBQF1ND\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_CCST\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_CCRT\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_WCSPF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_RFPE\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_TOA\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_TOT\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_TLGA\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_THGT\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_TPA\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_TPT\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_FPLP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_USCF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_IOC2T\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_GLV1P\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_GLV2P\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_ARFCLP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_FRTP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_ICFBP\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_WCSOSA\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_WCSCF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_WCSA\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2102_WCSRS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2102_HSFCF\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2201_CH1\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2201_CH2\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2201_CH3\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2201_CH4\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2202_RM\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2202_RS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"14\", \"writeValue\": \"14\", \"dataType\": \"\"}, {\"id\": \"2202_RSV\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2203_CH5\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2203_CH6\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2203_CH7\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2203_CH8\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"1\", \"writeValue\": \"1\", \"dataType\": \"\"}, {\"id\": \"2301_FC1O\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"\"}, {\"id\": \"2301_FC1PS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2301_FC2O\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"\"}, {\"id\": \"2301_FC2PS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2301_FC3O\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"\"}, {\"id\": \"2301_FC3PS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2301_FC4O\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"\"}, {\"id\": \"2301_FC4PS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2302_FC5O\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"\"}, {\"id\": \"2302_FC5PS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2302_FC6O\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"\"}, {\"id\": \"2302_FC6PS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2302_FC7O\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"\"}, {\"id\": \"2302_FC7PS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2302_FC8O\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"2\", \"writeValue\": \"2\", \"dataType\": \"\"}, {\"id\": \"2302_FC8PS\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2303_FC1E\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2303_FC2E\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2303_FC3E\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2303_FC4E\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2303_FC5E\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2303_FC6E\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2303_FC7E\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2303_FC8E\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}, {\"id\": \"2303_RSV\", \"name\": \"\", \"ts\": 1757402132433, \"value\": \"0\", \"writeValue\": \"0\", \"dataType\": \"\"}]"
}
保护使能.html:804 
原始消息长度: 61331 字节