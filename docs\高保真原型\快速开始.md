# 快速开始指南

## Unity WebGL集成演示

### 🚀 一键启动

#### Windows用户
双击运行 `start-server.bat` 文件，自动启动本地服务器。

#### Linux/Mac用户
在终端中运行：
```bash
./start-server.sh
```

### 📱 手动启动

如果自动启动脚本无法运行，可以手动启动：

#### 使用Python
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### 使用Node.js
```bash
# 使用npx
npx serve . -p 8000

# 使用全局安装的serve
npm install -g serve
serve . -p 8000
```

### 🌐 访问页面

服务器启动后，在浏览器中访问：

- **Unity集成演示**: http://localhost:8000/unity-demo.html
- **模拟版本**: http://localhost:8000/index.html
- **基础演示**: http://localhost:8000/demo.html

### 🎮 Unity集成功能

#### 前置条件
1. 确保Unity WebGL构建文件位于 `../../webgl/` 目录
2. Unity场景需要实现以下消息接收方法：
   - `SwitchToOverviewPosition()` - 总览视角
   - `ToggleDeviceViewTour()` - 自动漫游
   - `ToggleExpand()` - 设备展开
   - `ResetView()` - 重置视角

#### 控制功能
- **视角控制**: 左侧面板的视角控制按钮
- **设备交互**: 点击3D场景中的设备指示器
- **全屏模式**: 工具栏的全屏按钮
- **截图功能**: 工具栏的截图按钮
- **性能监控**: 左侧面板显示实时FPS

### 🔧 故障排除

#### Unity加载失败
1. 检查Unity WebGL文件路径是否正确
2. 确保使用HTTP服务器（不是file://协议）
3. 检查浏览器控制台错误信息
4. 确认Unity构建文件完整性

#### 跨域问题
- 必须使用HTTP服务器运行，不能直接打开HTML文件
- 推荐使用Chrome浏览器获得最佳性能

#### 性能问题
1. 关闭其他占用GPU的应用程序
2. 在浏览器设置中启用硬件加速
3. 使用较新版本的Chrome浏览器

### 📞 技术支持

如果遇到问题，请检查：
1. 浏览器控制台错误信息
2. Unity WebGL构建文件是否完整
3. 网络连接是否正常
4. 浏览器版本是否支持WebGL

---

**推荐浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
