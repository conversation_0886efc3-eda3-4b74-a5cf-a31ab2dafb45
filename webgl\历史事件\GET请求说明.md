# GET请求方式说明

## 修改概述

已将API请求方式从POST改为GET，这样可以：
- 减少CORS复杂性（GET请求比POST请求的CORS限制更少）
- 符合RESTful API设计规范（查询数据使用GET）
- 便于调试和测试（可以直接在浏览器地址栏测试）

## 请求格式

### 基本请求
```
GET https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list?pageNum=1&pageSize=50
```

### 带筛选的请求
```
GET https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59
```

## 参数说明

### 必需参数
- `pageNum`: 页码（从1开始）
- `pageSize`: 每页数量（建议50）

### 可选参数
- `alertName`: 事件类型筛选
  - 不传：查询所有事件
  - `报警设备`: 只查询报警事件
  - `故障设备`: 只查询故障事件
- `params[beginTime]`: 开始时间（格式：YYYY-MM-DD HH:mm:ss）
- `params[endTime]`: 结束时间（格式：YYYY-MM-DD HH:mm:ss）

## 请求头

### 必需的请求头
```
Accept: application/json
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg5MWQ5ZWYxLTE3ZjItNGVjYy05ZTU2LTQyZTA0MGQyMmVmMiJ9.C45TcywB-9WRYyfu5a-kkm4C9T5q1TNLygCN4yN3mPm3xRBdd35yD1NNzcsWMpa5ARysZymC6BvFFpWdzXLRoA
```

### 不再需要的请求头
- `Content-Type: application/json`（GET请求不需要）

## 代码实现

### JavaScript实现
```javascript
// 构建URL参数
const urlParams = new URLSearchParams();
urlParams.append('pageNum', 1);
urlParams.append('pageSize', 50);
urlParams.append('alertName', '报警设备');

// 时间范围参数使用params[beginTime]和params[endTime]格式
urlParams.append('params[beginTime]', '2025-07-17 00:00:00');
urlParams.append('params[endTime]', '2025-07-24 23:59:59');

// 发送GET请求
const apiUrl = 'https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list?' + urlParams.toString();
const response = await fetch(apiUrl, {
    method: 'GET',
    headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer [YOUR_TOKEN]'
    }
});
```

### curl命令测试
```bash
curl -X GET \
  'https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备&params[beginTime]=2025-07-17%2000:00:00&params[endTime]=2025-07-24%2023:59:59' \
  -H 'Accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3MmQwNWQyLTM4YTItNGRlMi04YmFlLWU2ZDhjMzQ1NmVlOSJ9.N6uHQtiEqCXKeGPeQdrdBDd4x5WXtTdi24-NtW51W1WOlDzoncX9-MiN7ra4w6xxtaFKCwN6kSka7XLEOE7wpg'
```

## CORS优势

### GET请求的CORS优势
1. **简单请求**: GET请求通常被视为"简单请求"，CORS限制较少
2. **无预检请求**: 不需要OPTIONS预检请求（在某些情况下）
3. **更好的兼容性**: 大多数服务器默认允许GET请求

### 服务器配置更简单
```nginx
# 只需要允许GET和OPTIONS方法
add_header Access-Control-Allow-Methods 'GET, OPTIONS';
```

## 测试方法

### 1. 浏览器直接测试
可以直接在浏览器地址栏输入URL进行测试（需要先登录获取token）

### 2. 开发者工具测试
1. 打开浏览器开发者工具
2. 切换到Network标签页
3. 刷新页面
4. 查看GET请求的详情

### 3. Postman测试
1. 创建新的GET请求
2. 设置URL和参数
3. 添加Authorization头
4. 发送请求查看结果

## 调试信息

### 控制台输出
页面会输出详细的调试信息：
```
GET请求URL: https://exdraw.qizhiyun.cc/prod-api/iot/alertLog/list?pageNum=1&pageSize=50&alertName=报警设备
请求参数: {pageNum: 1, pageSize: 50, alertName: "报警设备"}
API响应数据: {code: 200, msg: "查询成功", total: 1000, rows: [...]}
```

### 网络请求检查
在Network标签页中可以看到：
- 请求方法：GET
- 请求URL：包含所有查询参数
- 请求头：包含Authorization
- 响应状态：200 OK
- 响应数据：JSON格式的事件列表

## 兼容性说明

### 后端兼容性
如果后端API原本只支持POST请求，需要后端开发者：
1. 添加GET方法支持
2. 从URL参数中读取筛选条件
3. 保持响应格式不变

### 前端兼容性
前端代码已完全适配GET请求：
- 自动构建URL参数
- 正确设置请求头
- 保持数据处理逻辑不变

## 错误处理

### 常见错误
1. **400 Bad Request**: 参数格式错误
2. **401 Unauthorized**: 认证失败
3. **404 Not Found**: 接口地址错误
4. **405 Method Not Allowed**: 服务器不支持GET方法

### 解决方案
1. 检查URL参数格式
2. 验证Authorization token
3. 确认接口地址正确
4. 联系后端开发者确认GET方法支持

## 性能优势

### 缓存优势
GET请求可以被浏览器和代理服务器缓存，提高性能

### 网络优势
GET请求通常比POST请求更轻量，网络传输更快

### 调试优势
GET请求的参数在URL中可见，便于调试和日志记录

## 安全考虑

### URL长度限制
GET请求的参数在URL中，需要注意URL长度限制（通常2048字符）

### 敏感信息
认证token在请求头中，不会出现在URL中，保证安全性

### 日志记录
服务器访问日志会记录完整的GET请求URL，包含查询参数
