/* 动画效果和过渡 */

/* 关键帧动画定义 */
@keyframes headerGlow {
    0% {
        opacity: 0.5;
        box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
    }
    100% {
        opacity: 1;
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
    }
}

@keyframes logoSpin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

@keyframes rotating {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes loadingProgress {
    0% {
        width: 0%;
        opacity: 1;
    }
    50% {
        width: 70%;
        opacity: 1;
    }
    100% {
        width: 100%;
        opacity: 0.8;
    }
}

@keyframes warningBlink {
    0%, 100% {
        opacity: 1;
        box-shadow: 0 0 5px rgba(255, 170, 0, 0.3);
    }
    50% {
        opacity: 0.7;
        box-shadow: 0 0 15px rgba(255, 170, 0, 0.6);
    }
}

@keyframes modalSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes tooltipFadeIn {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes dataFlow {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes scanLine {
    0% {
        transform: translateY(-100%);
    }
    100% {
        transform: translateY(100vh);
    }
}

@keyframes glitch {
    0%, 100% {
        transform: translate(0);
    }
    20% {
        transform: translate(-2px, 2px);
    }
    40% {
        transform: translate(-2px, -2px);
    }
    60% {
        transform: translate(2px, 2px);
    }
    80% {
        transform: translate(2px, -2px);
    }
}

@keyframes neonGlow {
    0%, 100% {
        text-shadow: 
            0 0 5px var(--primary-color),
            0 0 10px var(--primary-color),
            0 0 15px var(--primary-color);
    }
    50% {
        text-shadow: 
            0 0 10px var(--primary-color),
            0 0 20px var(--primary-color),
            0 0 30px var(--primary-color),
            0 0 40px var(--primary-color);
    }
}

@keyframes circuitFlow {
    0% {
        stroke-dashoffset: 100;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        transform: translateX(-30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    0% {
        opacity: 0;
        transform: translateX(30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 页面加载动画 */
.app-container {
    animation: fadeInUp 0.8s ease-out;
}

.header {
    animation: fadeInUp 0.6s ease-out 0.1s both;
}

.left-panel {
    animation: fadeInLeft 0.8s ease-out 0.2s both;
}

.center-panel {
    animation: scaleIn 0.8s ease-out 0.3s both;
}

.right-panel {
    animation: fadeInRight 0.8s ease-out 0.4s both;
}

.footer {
    animation: fadeInUp 0.6s ease-out 0.5s both;
}

/* 悬停动画增强 */
.control-btn {
    position: relative;
    overflow: hidden;
}

.control-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.3) 0%, transparent 70%);
    transition: all 0.4s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.control-btn:hover::after {
    width: 200px;
    height: 200px;
}

/* 数据流动效果 */
.metric-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        var(--primary-color) 50%, 
        transparent 100%);
    animation: dataFlow 3s ease-in-out infinite;
}

/* 扫描线效果 */
.unity-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        var(--primary-color) 50%, 
        transparent 100%);
    animation: scanLine 4s linear infinite;
    z-index: 10;
    opacity: 0.6;
}

/* 霓虹灯文字效果 */
.logo-text {
    animation: neonGlow 3s ease-in-out infinite alternate;
}

/* 按钮点击波纹效果 */
.control-btn,
.toolbar-btn,
.chart-btn {
    position: relative;
    overflow: hidden;
}

.control-btn:active,
.toolbar-btn:active,
.chart-btn:active {
    transform: scale(0.98);
}

/* 添加点击波纹效果的类 */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 212, 255, 0.6);
    transform: scale(0);
    animation: rippleEffect 0.6s linear;
    pointer-events: none;
}

@keyframes rippleEffect {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* 状态指示器动画 */
.device-status.running::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

.device-status.warning::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    background: var(--warning-color);
    border-radius: 50%;
    animation: warningBlink 1s ease-in-out infinite;
}

/* 图表容器动画 */
.chart-container {
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        var(--primary-color) 50%, 
        transparent 100%);
    animation: dataFlow 2s ease-in-out infinite;
}

/* 加载状态动画 */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(0, 212, 255, 0.2) 50%, 
        transparent 100%);
    animation: dataFlow 1.5s ease-in-out infinite;
}

/* 错误状态动画 */
.error {
    animation: glitch 0.3s ease-in-out;
}

/* 成功状态动画 */
.success {
    animation: scaleIn 0.5s ease-out;
}

/* 响应式动画调整 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高性能动画优化 */
.control-btn,
.toolbar-btn,
.metric-card,
.device-item {
    will-change: transform;
}

.control-btn:hover,
.toolbar-btn:hover,
.metric-card:hover,
.device-item:hover {
    will-change: auto;
}

/* 3D变换动画 */
.metric-card {
    transform-style: preserve-3d;
    perspective: 1000px;
}

.metric-card:hover {
    transform: translateY(-2px) rotateX(5deg);
}

/* 渐变动画 */
@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.header::after {
    background: linear-gradient(90deg, 
        transparent 0%, 
        var(--primary-color) 25%,
        var(--accent-color) 50%,
        var(--primary-color) 75%,
        transparent 100%);
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

/* 粒子效果模拟 */
.unity-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, var(--primary-color), transparent),
        radial-gradient(2px 2px at 40px 70px, var(--accent-color), transparent),
        radial-gradient(1px 1px at 90px 40px, var(--primary-color), transparent),
        radial-gradient(1px 1px at 130px 80px, var(--accent-color), transparent),
        radial-gradient(2px 2px at 160px 30px, var(--primary-color), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: dataFlow 20s linear infinite;
    opacity: 0.1;
    pointer-events: none;
}
