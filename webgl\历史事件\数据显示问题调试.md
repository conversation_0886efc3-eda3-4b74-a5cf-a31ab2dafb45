# 数据显示问题调试指南

## 问题描述
第二页数据的接口请求成功，但是数据还是没有显示在页面上。

## 调试步骤

### 1. 打开浏览器开发者工具
按F12打开开发者工具，切换到Console标签页。

### 2. 执行调试命令
在控制台中输入以下命令来检查当前状态：

```javascript
debugCurrentState()
```

这会输出详细的调试信息，包括：
- 当前页码和总记录数
- 事件数据数量
- DOM元素状态
- 筛选条件等

### 3. 检查关键信息

#### 3.1 数据获取检查
点击下一页时，应该看到以下日志：
```
切换页码: {requestedPage: 2, currentPage: 1, totalPages: X, totalCount: X, pageSize: 50}
开始加载第 2 页数据
GET请求URL: https://...?pageNum=2&pageSize=50...
处理API数据: {total: X, rowsCount: X, currentPage: 2, pageSize: 50}
```

#### 3.2 数据处理检查
应该看到：
```
开始处理数据行，总数: X
处理第1条数据: {alertLogId: X, alertName: "报警设备", createTime: "..."}
成功处理事件 X: 事件名称
数据处理完成: {原始数据行数: X, 处理后事件数: X, 事件列表: [...]}
```

#### 3.3 筛选和渲染检查
应该看到：
```
应用筛选条件: {allEventsCount: X, filteredEventsCount: X, currentFilter: {...}}
渲染事件列表: {filteredEventsCount: X, currentPage: 2, totalCount: X, eventsContentExists: true}
渲染事件 1: {id: X, name: "...", time: "...", type: "报警设备"}
生成的HTML长度: X
DOM更新完成，当前eventsContent子元素数量: X
```

### 4. 常见问题排查

#### 问题1: 数据被过滤掉
**症状**: `处理后事件数: 0` 或很少
**原因**: API返回的数据中`alertName`字段不是"报警设备"或"故障设备"
**解决**: 检查API返回的实际数据格式

**调试命令**:
```javascript
// 查看原始API数据
console.log('最后一次API响应:', data);
```

#### 问题2: detail字段解析失败
**症状**: 看到"解析detail字段失败"警告
**原因**: detail字段不是有效的JSON格式
**解决**: 检查detail字段的实际格式

#### 问题3: DOM元素不存在
**症状**: `eventsContentExists: false`
**原因**: HTML结构问题或元素ID错误
**解决**: 检查HTML中是否存在id="eventsContent"的元素

#### 问题4: 时间格式问题
**症状**: 事件显示但时间为空或格式错误
**原因**: createTime字段格式不符合预期
**解决**: 检查时间格式化函数

### 5. 手动测试命令

#### 5.1 检查DOM元素
```javascript
const eventsContent = document.getElementById('eventsContent');
console.log('eventsContent元素:', eventsContent);
console.log('父元素:', eventsContent ? eventsContent.parentElement : null);
```

#### 5.2 手动渲染测试数据
```javascript
const testData = [{
    id: 999,
    alertName: '报警设备',
    eventName: '测试事件',
    createTime: '2025-01-24 12:00:00'
}];
filteredEvents = testData;
renderEventsList();
```

#### 5.3 检查CSS样式
```javascript
const eventsContent = document.getElementById('eventsContent');
if (eventsContent) {
    console.log('元素样式:', window.getComputedStyle(eventsContent));
    console.log('是否可见:', eventsContent.offsetHeight > 0);
}
```

### 6. 强制刷新数据

如果数据仍然不显示，尝试以下命令：

```javascript
// 清空当前数据
allEvents = [];
filteredEvents = [];

// 重新加载当前页
loadHistoryEvents();
```

### 7. 切换到模拟数据测试

如果真实API有问题，可以切换到模拟数据进行测试：

```javascript
enableMockDataMode();
changePage(2);
```

### 8. 检查网络请求

在Network标签页中检查：
1. 第二页的请求是否发送成功
2. 响应状态码是否为200
3. 响应数据格式是否正确
4. 响应数据中是否包含rows数组

### 9. 常见解决方案

#### 解决方案1: 数据格式问题
如果API返回的alertName字段值不同，修改过滤条件：
```javascript
// 临时修改过滤条件（在控制台执行）
// 先查看实际的alertName值
console.log('实际的alertName值:', data.rows.map(r => r.alertName));
```

#### 解决方案2: 强制显示所有数据
```javascript
// 临时移除过滤条件（在控制台执行）
allEvents = data.rows.map(item => ({
    id: item.alertLogId,
    alertName: item.alertName,
    eventName: item.detail ? JSON.parse(item.detail).name : '未知事件',
    createTime: item.createTime
}));
filteredEvents = [...allEvents];
renderEventsList();
```

#### 解决方案3: 检查CSS显示
```javascript
// 检查是否被CSS隐藏
const eventsContent = document.getElementById('eventsContent');
eventsContent.style.display = 'block';
eventsContent.style.visibility = 'visible';
```

### 10. 联系支持时提供的信息

如果问题仍然存在，请提供：
1. `debugCurrentState()` 的完整输出
2. 点击下一页时的完整控制台日志
3. Network标签页中第二页请求的详细信息
4. 浏览器类型和版本

通过以上步骤，应该能够定位数据不显示的具体原因。
