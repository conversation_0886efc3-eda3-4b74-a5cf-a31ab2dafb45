<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单元状态二进制位解析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1f2e;
            color: #ffffff;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(42, 49, 66, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-title {
            color: #00d4ff;
            font-size: 18px;
            margin-bottom: 15px;
        }
        .test-input {
            background: rgba(0, 212, 255, 0.08);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: #ffffff;
            padding: 8px 12px;
            border-radius: 4px;
            margin-right: 10px;
        }
        .test-button {
            background: rgba(0, 212, 255, 0.8);
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: rgba(0, 212, 255, 1);
        }
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .result-table th,
        .result-table td {
            border: 1px solid rgba(0, 212, 255, 0.3);
            padding: 8px;
            text-align: left;
        }
        .result-table th {
            background: rgba(0, 212, 255, 0.2);
        }
        .bit-1 {
            color: #00ff00;
            font-weight: bold;
        }
        .bit-0 {
            color: #888888;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>单元状态二进制位解析测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 二进制位解析测试</div>
            <p>输入一个16位以内的数值，查看其二进制位解析结果：</p>
            <input type="number" id="testValue" class="test-input" placeholder="输入数值 (如: 32769)" value="32769">
            <button class="test-button" onclick="testBinaryExpansion()">解析二进制位</button>
            
            <div id="binaryResult"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 单元状态参数展开测试</div>
            <p>模拟单元信息参数的展开过程：</p>
            <input type="text" id="unitId" class="test-input" placeholder="单元ID (如: A1)" value="A1">
            <button class="test-button" onclick="testParameterExpansion()">展开参数</button>
            
            <div id="parameterResult"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 数据值展开测试</div>
            <p>模拟实际数据值的展开过程：</p>
            <input type="text" id="identifier" class="test-input" placeholder="标识符 (如: HMI_30490)" value="HMI_30490">
            <input type="number" id="dataValue" class="test-input" placeholder="数据值 (如: 32769)" value="32769">
            <button class="test-button" onclick="testDataExpansion()">展开数据值</button>
            
            <div id="dataResult"></div>
        </div>
    </div>

    <script>
        // 定义16个状态位的名称模板
        const statusNameTemplates = [
            '下行光纤断',      // 第1位（最高位）
            '封锁',           // 第2位
            '停止',           // 第3位
            '启动',           // 第4位
            '电源故障',       // 第5位
            '上行光纤断',     // 第6位
            '下行光纤丢同步', // 第7位
            '下行光纤无光',   // 第8位
            '过压报警',       // 第9位
            '超温',           // 第10位
            '欠压',           // 第11位
            '过压',           // 第12位
            '过流4',          // 第13位
            '过流3',          // 第14位
            '过流2',          // 第15位
            '过流1'           // 第16位（最低位）
        ];

        function testBinaryExpansion() {
            const value = parseInt(document.getElementById('testValue').value) || 0;
            const resultDiv = document.getElementById('binaryResult');
            
            let html = `
                <h4>数值 ${value} 的二进制位解析：</h4>
                <p>二进制表示：${value.toString(2).padStart(16, '0')}</p>
                <table class="result-table">
                    <thead>
                        <tr>
                            <th>位序号</th>
                            <th>位位置</th>
                            <th>位值</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            for (let i = 0; i < 16; i++) {
                const bitValue = (value >> (15 - i)) & 1;
                const bitClass = bitValue === 1 ? 'bit-1' : 'bit-0';
                const status = bitValue === 1 ? '激活' : '未激活';
                
                html += `
                    <tr>
                        <td>第${i + 1}位</td>
                        <td>位${15 - i}</td>
                        <td class="${bitClass}">${bitValue}</td>
                        <td class="${bitClass}">${status}</td>
                    </tr>
                `;
            }
            
            html += '</tbody></table>';
            resultDiv.innerHTML = html;
        }

        function testParameterExpansion() {
            const unitId = document.getElementById('unitId').value || 'A1';
            const resultDiv = document.getElementById('parameterResult');
            
            let html = `
                <h4>单元信息${unitId} 参数展开结果：</h4>
                <table class="result-table">
                    <thead>
                        <tr>
                            <th>位序号</th>
                            <th>原始标识符</th>
                            <th>新标识符</th>
                            <th>参数名称</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            const originalIdentifier = 'HMI_30490';
            
            for (let i = 0; i < 16; i++) {
                const newIdentifier = `${originalIdentifier}_${i}`;
                const parameterName = `${unitId}${statusNameTemplates[i]}`;
                
                html += `
                    <tr>
                        <td>第${i + 1}位</td>
                        <td>${originalIdentifier}</td>
                        <td>${newIdentifier}</td>
                        <td>${parameterName}</td>
                    </tr>
                `;
            }
            
            html += '</tbody></table>';
            resultDiv.innerHTML = html;
        }

        function testDataExpansion() {
            const identifier = document.getElementById('identifier').value || 'HMI_30490';
            const value = parseInt(document.getElementById('dataValue').value) || 0;
            const resultDiv = document.getElementById('dataResult');
            
            let html = `
                <h4>数据值展开结果：</h4>
                <p>原始数据：{"${identifier}": ${value}}</p>
                <p>二进制表示：${value.toString(2).padStart(16, '0')}</p>
                <h5>展开后的数据值对象：</h5>
                <table class="result-table">
                    <thead>
                        <tr>
                            <th>位序号</th>
                            <th>新标识符</th>
                            <th>位值</th>
                            <th>数据对象</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            for (let i = 0; i < 16; i++) {
                const bitValue = (value >> (15 - i)) & 1;
                const newIdentifier = `${identifier}_${i}`;
                const bitClass = bitValue === 1 ? 'bit-1' : 'bit-0';
                
                html += `
                    <tr>
                        <td>第${i + 1}位</td>
                        <td>${newIdentifier}</td>
                        <td class="${bitClass}">${bitValue}</td>
                        <td>{"${newIdentifier}": ${bitValue}}</td>
                    </tr>
                `;
            }
            
            html += '</tbody></table>';
            resultDiv.innerHTML = html;
        }

        // 页面加载时执行默认测试
        window.addEventListener('load', function() {
            testBinaryExpansion();
            testParameterExpansion();
            testDataExpansion();
        });
    </script>
</body>
</html>
