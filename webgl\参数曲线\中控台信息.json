参数曲线.html:802 === 初始化参数曲线页面 ===
参数曲线.html:803 API配置: {baseUrl: 'https://exdraw.qizhiyun.cc/prod-api', headers: {…}}
参数曲线.html:1390 参数图表初始化完成
参数曲线.html:1431 切换时间模式: realtime
参数曲线.html:624 开始获取设备列表...
参数曲线.html:817 参数曲线页面初始化完成
参数曲线.html:639 设备列表API响应: {code: 200, msg: '查询成功', total: 9, rows: Array(9)}
参数曲线.html:953 已填充9个设备到下拉框
参数曲线.html:644 成功获取9个设备
ServiceWorker.js:24 [Service Worker] Fetching resource: https://cdp.cloud.unity3d.com/v1/events
ServiceWorker.js:24 [Service Worker] Fetching resource: https://cdp.cloud.unity3d.com/v1/events
ServiceWorker.js:29 [Service Worker] Caching new resource: https://cdp.cloud.unity3d.com/v1/events
ServiceWorker.js:30 Uncaught (in promise) TypeError: Failed to execute 'put' on 'Cache': Request method 'POST' is unsupported
    at ServiceWorker.js:30:13
(anonymous) @ ServiceWorker.js:30
await in (anonymous)
(anonymous) @ ServiceWorker.js:32
ServiceWorker.js:24 [Service Worker] Fetching resource: https://cdp.cloud.unity3d.com/v1/events
ServiceWorker.js:29 [Service Worker] Caching new resource: https://cdp.cloud.unity3d.com/v1/events
ServiceWorker.js:30 Uncaught (in promise) TypeError: Failed to execute 'put' on 'Cache': Request method 'POST' is unsupported
    at ServiceWorker.js:30:13
(anonymous) @ ServiceWorker.js:30
await in (anonymous)
(anonymous) @ ServiceWorker.js:32
The FetchEvent for "https://cdp.cloud.unity3d.com/v1/events" resulted in a network error response: the promise was rejected.
Promise.then
(anonymous) @ ServiceWorker.js:22
ServiceWorker.js:27  Uncaught (in promise) TypeError: Failed to fetch
    at ServiceWorker.js:27:24
(anonymous) @ ServiceWorker.js:27
await in (anonymous)
(anonymous) @ ServiceWorker.js:32
ServiceWorker.js:24 [Service Worker] Fetching resource: https://cdp.cloud.unity3d.com/v1/events
ServiceWorker.js:29 [Service Worker] Caching new resource: https://cdp.cloud.unity3d.com/v1/events
ServiceWorker.js:30 Uncaught (in promise) TypeError: Failed to execute 'put' on 'Cache': Request method 'POST' is unsupported
    at ServiceWorker.js:30:13
(anonymous) @ ServiceWorker.js:30
await in (anonymous)
(anonymous) @ ServiceWorker.js:32
参数曲线.html:1060 === 设备选择变化 ===
参数曲线.html:1061 选择的设备ID: 288
参数曲线.html:1062 当前设备列表: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
参数曲线.html:1073 找到的设备: {deviceId: 288, deviceName: '电气拓扑', productId: 189, productName: '电气拓扑', deviceType: 1, …}
参数曲线.html:1077 设置当前设备: {deviceId: 288, deviceName: '电气拓扑', productId: 189, productName: '电气拓扑', deviceType: 1, …}
参数曲线.html:1084 开始获取参数模型...
参数曲线.html:678 === 获取参数模型 ===
参数曲线.html:679 设备ID: 288
参数曲线.html:680 当前设备: {deviceId: 288, deviceName: '电气拓扑', productId: 189, productName: '电气拓扑', deviceType: 1, …}
参数曲线.html:683 请求URL: https://exdraw.qizhiyun.cc/prod-api/iot/device/listThingsModel?deviceId=288&pageNum=1&pageSize=9999
参数曲线.html:684 请求头: {Accept: 'application/json', Authorization: 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleS…W51W1WOlDzoncX9-MiN7ra4w6xxtaFKCwN6kSka7XLEOE7wpg'}
参数曲线.html:693 响应状态: 200 
参数曲线.html:694 响应头: {content-type: 'application/json'}
参数曲线.html:701 参数模型API响应: {code: 200, msg: '查询成功', total: 22, rows: Array(22)}
参数曲线.html:702 响应数据类型: object
参数曲线.html:703 data.code: 200
参数曲线.html:704 data.rows: (22) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
参数曲线.html:705 data.rows长度: 22
参数曲线.html:960 === 填充参数控制列表 ===
参数曲线.html:961 参数数组: (22) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
参数曲线.html:962 参数数量: 22
参数曲线.html:965 容器元素: <div id=​"parameterControlList">​…​</div>​
参数曲线.html:974 已清空容器内容
参数曲线.html:982 开始创建参数控制项...
参数曲线.html:985 创建第1个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第1个参数控制项已添加到容器
参数曲线.html:985 创建第2个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第2个参数控制项已添加到容器
参数曲线.html:985 创建第3个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第3个参数控制项已添加到容器
参数曲线.html:985 创建第4个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第4个参数控制项已添加到容器
参数曲线.html:985 创建第5个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第5个参数控制项已添加到容器
参数曲线.html:985 创建第6个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第6个参数控制项已添加到容器
参数曲线.html:985 创建第7个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第7个参数控制项已添加到容器
参数曲线.html:985 创建第8个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第8个参数控制项已添加到容器
参数曲线.html:985 创建第9个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第9个参数控制项已添加到容器
参数曲线.html:985 创建第10个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第10个参数控制项已添加到容器
参数曲线.html:985 创建第11个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第11个参数控制项已添加到容器
参数曲线.html:985 创建第12个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第12个参数控制项已添加到容器
参数曲线.html:985 创建第13个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第13个参数控制项已添加到容器
参数曲线.html:985 创建第14个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第14个参数控制项已添加到容器
参数曲线.html:985 创建第15个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第15个参数控制项已添加到容器
参数曲线.html:985 创建第16个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第16个参数控制项已添加到容器
参数曲线.html:985 创建第17个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第17个参数控制项已添加到容器
参数曲线.html:985 创建第18个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第18个参数控制项已添加到容器
参数曲线.html:985 创建第19个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第19个参数控制项已添加到容器
参数曲线.html:985 创建第20个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第20个参数控制项已添加到容器
参数曲线.html:985 创建第21个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第21个参数控制项已添加到容器
参数曲线.html:985 创建第22个参数控制项: {createBy: null, createTime: null, updateBy: null, updateTime: null, remark: null, …}
参数曲线.html:988 第22个参数控制项已添加到容器
参数曲线.html:995 参数计数已更新: (电气拓扑 - 22个参数)
参数曲线.html:998 ✅ 成功创建22个参数控制项
参数曲线.html:999 最终容器内容: <div class="parameter-control-item" data-identifier="HMI_32048">
                <div class="parameter-info">
                    <div class="parameter-color" style="background-color: #ff6b6b"></div>
                    <div class="parameter-details">
                        <div class="parameter-name" title="负载无功功率">负载无功功率</div>
                        <div class="parameter-identifier">HMI_32048</div>
                    </div>
                </div>
                <div class="parameter-controls">
                    <label class="toggle-switch">
                        <input type="checkbox" class="parameter-toggle" data-identifier="HMI_32048">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div><div class="parameter-control-item" data-identifier="HMI_32050">
                <div class="parameter-info">
                    <div class="parameter-color" style="background-color: #4ecdc4"></div>
                    <div class="parameter-details">
                        <div class="parameter-name" title="功率因数">功率因数</div>
                        <div class="parameter-identifier">HMI_32050</div>
                    </div>
                </div>
                <div class="parameter-controls">
                    <label class="toggle-switch">
                        <input type="checkbox" class="parameter-toggle" data-identifier="HMI_32050">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div><div class="parameter-control-item" data-identifier="HMI_32030">
                <div class="parameter-info">
                    <div class="parameter-color" style="background-color: #45b7d1"></div>
                    <div class="parameter-details">
                        <div class="parameter-name" title="母线电压Uab">母线电压Uab</div>
                        <div class="parameter-identifier">HMI_32030</div>
                    </div>
                </div>
                <div class="parameter-controls">
                    <label class="toggle-switch">
                        <input type="checkbox" class="parameter-toggle" data-identifier="HMI_32030">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div><div class="parameter-control-item" data-identifier="HMI_32032">
                <div class="parameter-info">
                    <div class="parameter-color" style="background-color: #96ceb4"></div>
                    <div class="parameter-details">
                        <div class="parameter-name" title="母线电压Ubc">母线电压Ubc</div>
                        <div class="parameter-identifier">HMI_32032</div>
                    </div>
                </div>
                <div class="parameter-controls">
                    <label class="toggle-switch">
                        <input type="checkbox" class="parameter-toggle" data-identifier="HMI_32032">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div><div class="parameter-control-item" data-identifier="HMI_32034">
                <div class="parameter-info">
                    <div class="parameter-color" style="background-color: #feca57"></div>
                    <div class="parameter-details">
                        <div class="parameter-name" title="母线电压Uca">母线电压Uca</div>
                        <div class="parameter-identifier">HMI_32034</div>
                    </div>
                </div>
                <div class="parameter-controls">
                    <label class="toggle-switch">
                        <input type="checkbox" class="parameter-toggle" data-identifier="HMI_32034">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div><div class="parameter-control-item" data-identifier="HMI_32040">
                <div class="parameter-info">
                    <div class="parameter-color" style="background-color: #ff9ff3"></div>
                    <div class="parameter-details">
                        <div class="parameter-name" title="SVG电流la">SVG电流la</div>
                        <div class="parameter-identifier">HMI_32040</div>
                    </div>
                </div>
                <div class="parameter-controls">
                    <label class="toggle-switch">
                        <input type="checkbox" class="parameter-toggle" data-identifier="HMI_32040">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div><div class="parameter-control-item" data-identifier="HMI_32042">
                <div class="parameter-info">
                    <div class="parameter-color" style="background-color: #54a0ff"></div>
                    <div class="parameter-details">
                        <div
参数曲线.html:710 成功获取22个参数模型
参数曲线.html:1047 默认开启参数: HMI_32048
参数曲线.html:1047 默认开启参数: HMI_32050
参数曲线.html:1047 默认开启参数: HMI_32030
参数曲线.html:756 开始获取历史数据...
参数曲线.html:760 历史数据请求参数: {deviceId: 288, serialNumber: 'D19QBHKRZ791U', identifierList: Array(3), beginTime: '2025-07-24 13:56:01', endTime: '2025-07-25 13:56:01'}
参数曲线.html:779 历史数据API响应: {msg: '操作成功', code: 200, data: Array(9671)}
参数曲线.html:1142 开始处理历史数据，数据点数量: 9671
参数曲线.html:1191 历史数据处理完成: {times: Array(9671), series: {…}}
参数曲线.html:1302 图表更新完成
参数曲线.html:785 成功获取9671个时间点的历史数据
