#!/usr/bin/env node
const path = require('path');
const express = require('express');

// Create express application
const app = express();

// Settings
const hostname = '0.0.0.0'; // 这允许所有网络接口访问
const port = 62411;
const enableCORS = true; 
const enableWasmMultithreading = true;

// Serve the current working directory 
const unityBuildPath = __dirname;

app.use((req, res, next) => {
    var path = req.url;

    // 添加阻止 IDM 拦截的头
    res.set('Content-Disposition', 'inline');
    
    // Provide COOP, COEP and CORP headers for SharedArrayBuffer
    // multithreading: https://web.dev/coop-coep/
    if (enableWasmMultithreading &&
        (
            path == '/' ||
            path.includes('.js') ||
            path.includes('.html') ||
            path.includes('.htm')
        )
    ) {
        res.set('Cross-Origin-Opener-Policy', 'same-origin');
        res.set('Cross-Origin-Embedder-Policy', 'require-corp');
        res.set('Cross-Origin-Resource-Policy', 'cross-origin');
    }

    // Set CORS headers
    if (enableCORS) {
        res.set('Access-Control-Allow-Origin', '*');
    }    

    // Set content encoding depending on compression
    if (path.endsWith('.br')) {
        res.set('Content-Encoding', 'br');
    } else if (path.endsWith('.gz')) {
        res.set('Content-Encoding', 'gzip');
    }

    // Explicitly set content type
    if (path.includes('.wasm')) {
        res.set('Content-Type', 'application/wasm');
    } else if (path.includes('.js')) {
        res.set('Content-Type', 'application/javascript');
    } else if (path.includes('.json')) {
        res.set('Content-Type', 'application/json');
    } else if (
    path.includes('.data') ||
    path.includes('.bundle') ||
    path.endsWith('.unityweb')
    ) {res.set('Content-Type', 'application/octet-stream');
    }

    // Ignore cache-control: no-cache 
    if (req.headers['cache-control'] == 'no-cache' &&
    (
        req.headers['if-modified-since'] ||
        req.headers['if-none-match']
    )
    ) {       
        delete req.headers['cache-control'];
    }        

    next();
});

app.use('/', express.static(unityBuildPath, { immutable: true }));

const server = app.listen(port, hostname, () => {
    const os = require('os');
    const networkInterfaces = os.networkInterfaces();
    let localIp = 'localhost';
    
    // 获取本机IPv4地址
    Object.keys(networkInterfaces).forEach(interfaceName => {
        networkInterfaces[interfaceName].forEach(interface => {
            if (interface.family === 'IPv4' && !interface.internal) {
                localIp = interface.address;
            }
        });
    });

    console.log(`Web server serving directory ${unityBuildPath} at http://${hostname}:${port}`);
    console.log(`For local access: http://localhost:${port}/main.html`);
    console.log(`For network access: http://${localIp}:${port}/main.html`);
});

server.addListener('error', (error) => {
    console.error(error);
});

server.addListener('close', () => {
    console.log('Server stopped.');
    process.exit();
});



