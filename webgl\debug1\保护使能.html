<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保护使能 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>保护使能</h1>
        </div>

        <div class="main-content">
            <div class="protection-panel">
                <div class="panel-title">保护功能使能1</div>
                <table class="params-table">
                    <thead>
                        <tr>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                    </tr>
                    </thead>
                    <tbody id="protection-table-1">
                        <!-- 保护使能参数1-32将在这里动态生成 -->
                    </tbody>
                </table>
            </div>

            <div class="protection-panel">
                <div class="panel-title">保护功能使能2</div>
                <table class="params-table">
                    <thead>
                        <tr>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                    </tr>
                    </thead>
                    <tbody id="protection-table-2">
                        <!-- 保护使能参数33-64将在这里动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
        <button class="send-button" id="send-settings-btn" onclick="sendParameterSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
    <script>
        /**
         * 保护使能页面配置
         * 定义保护使能参数列表和页面设置
         */

        // 保护功能使能1组 (32个参数)
        const protectionGroup1 = [
            { mqttId: '2101_GOA1', name: '电网线电压有效值Ⅰ段过压报警使能' },
            { mqttId: '2101_GOP2', name: '电网线电压有效值Ⅱ段过压保护使能' },
            { mqttId: '2101_GOP3', name: '电网线电压有效值Ⅲ段过压保护使能' },
            { mqttId: '2101_GIOP', name: '电网线电压瞬时值过压保护使能' },
            { mqttId: '2101_GUA1', name: '电网线电压有效值Ⅰ段欠压报警使能' },
            { mqttId: '2101_GUP2', name: '电网线电压有效值Ⅱ段欠压保护使能' },
            { mqttId: '2101_GUP3', name: '电网线电压有效值Ⅲ段欠压保护使能' },
            { mqttId: '2101_GVIP', name: '电网线电压有效值不平衡保护使能' },
            { mqttId: '2101_GVPLP', name: '电网线电压缺相保护使能' },
            { mqttId: '2101_GVRSP', name: '电网电压反序保护使能' },
            { mqttId: '2101_SSLP', name: '同步信号丢失保护使能' },
            { mqttId: '2101_SHSFP', name: 'SVG侧霍尔传感器故障保护使能' },
            { mqttId: '2101_ABSSCFP', name: '模拟板采样通道自检故障保护使能' },
            { mqttId: '2101_ZSVOP', name: '零序电压超标保护使能' },
            { mqttId: '2101_SOCOA1', name: 'SVG输出电流有效值Ⅰ段过流报警使能' },
            { mqttId: '2101_SOCOP2', name: 'SVG输出电流有效值Ⅱ段过流保护使能' },
            { mqttId: '2101_SOCIOP', name: 'SVG输出电流瞬时值过流保护使能' },
            { mqttId: '2101_SOCHOP', name: 'SVG输出电流硬件过流保护使能' },
            { mqttId: '2101_SOCPLP', name: 'SVG输出电流缺相保护使能' },
            { mqttId: '2101_SOCCLA', name: 'SVG输出电流指令限幅报警使能' },
            { mqttId: '2101_SICOCDFP', name: 'SVG瞬时电流过流(CT检测方式)故障保护使能' },
            { mqttId: '2101_PTFP', name: 'PT故障保护使能' },
            { mqttId: '2101_SZSCFP', name: 'SVG零序电流故障保护使能' },
            { mqttId: '2101_PUGF', name: '功率单元状态一般性故障使能' },
            { mqttId: '2101_PUUOP', name: '功率单元UDC过压保护使能' },
            { mqttId: '2101_PUUIP', name: '功率单元UDC不平衡保护使能' },
            { mqttId: '2101_PUHP', name: '功率单元硬件保护(HW)使能' },
            { mqttId: '2101_PUSCFP', name: '功率单元自检故障保护使能' },
            { mqttId: '2101_PUSIP', name: '功率单元状态不一致保护使能' },
            { mqttId: '2101_RS485CTF', name: 'RS485通信超时故障使能' },
            { mqttId: '2101_RS485CCF', name: 'RS485通信校验故障使能' },
            { mqttId: '2101_DRTF', name: 'DRAM读超时故障使能' }
        ];

        // 保护功能使能2组 (32个参数)
        const protectionGroup2 = [
            { mqttId: '2102_DF', name: 'DRAM故障使能' },
            { mqttId: '2102_DWPF', name: 'DRAM写参数故障使能' },
            { mqttId: '2102_WWRP', name: 'WDI看门狗复位保护使能' },
            { mqttId: '2102_CTF', name: '充电超时故障使能' },
            { mqttId: '2102_TSNCSDF', name: '行程开关未合故障使能' },
            { mqttId: '2102_CCK1NE', name: '充电接触器K1不吸合使能' },
            { mqttId: '2102_CCK1ND', name: '充电接触器K1不分开使能' },
            { mqttId: '2102_CBQF1NE', name: '断路器QF1不吸合使能' },
            { mqttId: '2102_CBQF1ND', name: '断路器QF1不分开使能' },
            { mqttId: '2102_CCST', name: 'CAN通信发送超时使能' },
            { mqttId: '2102_CCRT', name: 'CAN通信接收超时使能' },
            { mqttId: '2102_WCSPF', name: '水冷系统电源故障使能' },
            { mqttId: '2102_RFPE', name: '读铁电参数错误使能' },
            { mqttId: '2102_TOA', name: '变压器超温报警使能' },
            { mqttId: '2102_TOT', name: '变压器超温跳闸使能' },
            { mqttId: '2102_TLGA', name: '变压器轻瓦斯报警使能' },
            { mqttId: '2102_THGT', name: '变压器重瓦斯跳闸使能' },
            { mqttId: '2102_TPA', name: '变压器压力报警使能' },
            { mqttId: '2102_TPT', name: '变压器压力跳闸使能' },
            { mqttId: '2102_FPLP', name: '风机缺相保护使能' },
            { mqttId: '2102_USCF', name: '单元短路故障使能' },
            { mqttId: '2102_IOC2T', name: '瞬时过流2跳闸使能' },
            { mqttId: '2102_GLV1P', name: '电网低电压1保护使能' },
            { mqttId: '2102_GLV2P', name: '电网低电压2保护使能' },
            { mqttId: '2102_ARFCLP', name: '自动恢复失败次数超限保护使能' },
            { mqttId: '2102_FRTP', name: '故障复位超时保护使能' },
            { mqttId: '2102_ICFBP', name: '柜间光纤断保护使能' },
            { mqttId: '2102_WCSOSA', name: '水冷系统运行状态异常使能' },
            { mqttId: '2102_WCSCF', name: '水冷系统综合故障使能' },
            { mqttId: '2102_WCSA', name: '水冷系统报警使能' },
            { mqttId: '2102_WCSRS', name: '水冷系统请求停止使能' },
            { mqttId: '2102_HSFCF', name: '高速光纤通信故障使能' }
        ];

        // 合并所有参数
        const allProtectionParameters = [...protectionGroup1, ...protectionGroup2];

        // 页面配置对象
        const protectionConfig = {
            pageTitle: '保护使能',
            panelTitles: ['保护功能使能1', '保护功能使能2'],
            parameters: allProtectionParameters,
            parametersPerPanel: 32,
            parametersPerRow: 2
        };

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('保护使能配置页面初始化...');

            // 使用通用参数配置管理器初始化页面
            initParameterConfigPage(protectionConfig);
        });
    </script>
</body>
</html>
