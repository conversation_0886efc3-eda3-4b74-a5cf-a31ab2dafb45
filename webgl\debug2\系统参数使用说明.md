# 系统参数页面使用说明

## 页面概述

系统参数页面基于控制模式页面的模板创建，用于配置和管理SVG数字化系统的62个核心系统参数。

## 页面特性

### 1. 整体布局
- **单面板设计**：所有62个参数集中在一个面板中显示
- **表格布局**：每行显示4个参数，共16行（最后一行显示2个参数）
- **响应式设计**：适配大屏显示，保持工业监控界面风格

### 2. 参数输入控件
- **控件类型**：浮点数输入框（HTML input type="number"）
- **数值范围**：-99999.00 到 99999.00
- **小数位数**：固定保留2位小数
- **输入验证**：自动验证输入值范围，超出范围时自动修正

### 3. 表格结构
每个参数包含以下列：
- **序号**：参数编号（1-62）
- **参数名称**：参数的中文名称
- **设定值**：可编辑的浮点数输入框
- **当前值**：从MQTT接收的实时数值
- **单位**：参数单位（如A、V、Hz、°等）

## 62个系统参数列表

| 序号 | 参数名称 | 标识符 | 单位 |
|------|----------|--------|------|
| 1 | 额定电流 | SVG_1901 | A |
| 2 | 额定电压 | SVG_1902 | V |
| 3 | 电流定标系数 | SVG_1903 | - |
| 4 | 直流侧额定电压 | SVG_1904 | V |
| 5 | 电网频率 | SVG_1905 | Hz |
| 6 | 单元载波频率 | SVG_1906 | Hz |
| 7 | 连接电感 | SVG_1907 | mH |
| 8 | 直流侧电压指令值 | SVG_1908 | V |
| 9 | 斜坡速率 | SVG_1909 | - |
| 10 | 斜坡输出最小值 | SVG_1910 | - |
| ... | ... | ... | ... |
| 62 | q 轴负向辅助校正系数 | SVG_1965 | - |

*（完整列表包含所有62个参数）*

## 功能特性

### 1. 实时数据同步
- **MQTT连接**：自动连接MQTT服务器获取实时数据
- **数据更新**：实时显示当前值，10秒更新间隔
- **首次同步**：页面加载时自动将设定值同步为当前值

### 2. 参数编辑
- **浮点数输入**：支持小数点后2位精度
- **范围验证**：自动验证输入范围（-99999.00 到 99999.00）
- **实时高亮**：修改的参数会高亮显示
- **错误处理**：无效输入自动恢复为默认值

### 3. 参数下载
- **批量发送**：点击"下载"按钮批量发送所有修改的参数
- **状态反馈**：显示发送进度和结果
- **错误处理**：网络异常时提供错误提示

## 技术实现

### 1. 继承架构
```javascript
class SystemParameterManager extends ParameterConfigManager {
    // 扩展通用参数管理器
    // 专门处理浮点数输入控件
}
```

### 2. 样式继承
- 基础样式：`../common/parameter-config.css`
- 专用样式：页面内嵌CSS，针对浮点数输入框优化

### 3. 数据格式
```javascript
{
    mqttId: 'SVG_1901',    // MQTT标识符
    name: '额定电流',       // 参数名称
    unit: 'A',            // 单位
    currentValue: 0.00,   // 当前值（浮点数）
    settingValue: 0.00    // 设定值（浮点数）
}
```

## 使用方法

### 1. 页面访问
```
file:///c:/Users/<USER>/source/BaiYunGroup/webgl/debug2/系统参数.html
```

### 2. 参数修改
1. 在设定值列的输入框中输入新数值
2. 系统自动验证输入范围
3. 修改的参数行会高亮显示
4. 点击"下载"按钮发送到设备

### 3. 状态监控
- **MQTT状态**：右上角显示连接状态
- **数据时间戳**：显示最后更新时间
- **参数高亮**：修改的参数会黄色高亮

## 注意事项

1. **数值精度**：所有参数值保留2位小数
2. **输入范围**：必须在-99999.00到99999.00之间
3. **网络连接**：需要MQTT连接才能接收实时数据和发送参数
4. **浏览器兼容**：建议使用Chrome或Edge浏览器

## 文件结构

```
webgl/debug2/
├── 系统参数.html          # 主页面文件
├── 系统参数使用说明.md     # 本说明文档
└── ../common/
    ├── parameter-config.css  # 通用样式
    └── parameter-config.js   # 通用脚本
```

## 维护说明

如需添加或修改参数：
1. 修改`systemParametersGroup`数组
2. 更新`parametersPerPanel`配置
3. 确保参数总数与实际数量一致
