{"code": 200, "msg": "查询成功", "total": 9, "rows": [{"deviceId": 293, "deviceName": "主控辅控", "productId": 194, "productName": "主控辅控", "deviceType": 1, "tenantId": 19, "tenantName": "白云电器", "serialNumber": "D19B21NJRP35I", "firmwareVersion": 1, "status": 3, "isShadow": 0, "rssi": 0, "thingsModelValue": null, "activeTime": "2025-07-22", "createTime": "2025-07-22", "gwDevCode": null, "locationWay": 1, "imgUrl": "", "isOwner": 1, "subDeviceCount": 0, "slaveId": null, "transport": "MQTT", "protocolCode": "JSON", "deptName": "白云电器", "createBy": "bydq_admin", "canSeeCode": true, "alertCount": null, "guid": null, "thingsModels": null, "stringList": [], "integerList": [], "decimalList": [], "enumList": [], "arrayList": [], "boolList": [], "readOnlyList": []}, {"deviceId": 292, "deviceName": "定值设备", "productId": 193, "productName": "定值设备", "deviceType": 1, "tenantId": 19, "tenantName": "白云电器", "serialNumber": "D19V130SX3SY0", "firmwareVersion": 1, "status": 3, "isShadow": 0, "rssi": 0, "thingsModelValue": null, "activeTime": "2025-07-21", "createTime": "2025-07-21", "gwDevCode": null, "locationWay": 1, "imgUrl": "", "isOwner": 1, "subDeviceCount": 0, "slaveId": null, "transport": "MQTT", "protocolCode": "JSON", "deptName": "白云电器", "createBy": "bydq_admin", "canSeeCode": true, "alertCount": null, "guid": null, "thingsModels": null, "stringList": [], "integerList": [], "decimalList": [], "enumList": [], "arrayList": [], "boolList": [], "readOnlyList": []}, {"deviceId": 288, "deviceName": "电气拓扑", "productId": 189, "productName": "电气拓扑", "deviceType": 1, "tenantId": 19, "tenantName": "白云电器", "serialNumber": "D19QBHKRZ791U", "firmwareVersion": 1, "status": 3, "isShadow": 0, "rssi": 0, "thingsModelValue": null, "activeTime": "2025-07-18", "createTime": "2025-07-18", "gwDevCode": null, "locationWay": 1, "imgUrl": "", "isOwner": 1, "subDeviceCount": 0, "slaveId": null, "transport": "MQTT", "protocolCode": "JSON", "deptName": "白云电器", "createBy": "bydq_admin", "canSeeCode": true, "alertCount": null, "guid": null, "thingsModels": null, "stringList": [], "integerList": [], "decimalList": [], "enumList": [], "arrayList": [], "boolList": [], "readOnlyList": []}, {"deviceId": 287, "deviceName": "IO状态", "productId": 188, "productName": "IO状态", "deviceType": 1, "tenantId": 19, "tenantName": "白云电器", "serialNumber": "D19QDKG1WYPE6", "firmwareVersion": 1, "status": 3, "isShadow": 0, "rssi": 0, "thingsModelValue": null, "activeTime": "2025-07-17", "createTime": "2025-07-17", "gwDevCode": null, "locationWay": 1, "imgUrl": "", "isOwner": 1, "subDeviceCount": 0, "slaveId": null, "transport": "MQTT", "protocolCode": "JSON", "deptName": "白云电器", "createBy": "bydq_admin", "canSeeCode": true, "alertCount": null, "guid": null, "thingsModels": null, "stringList": [], "integerList": [], "decimalList": [], "enumList": [], "arrayList": [], "boolList": [], "readOnlyList": []}, {"deviceId": 286, "deviceName": "DSP状态", "productId": 187, "productName": "DSP状态", "deviceType": 1, "tenantId": 19, "tenantName": "白云电器", "serialNumber": "D195XOODF4707", "firmwareVersion": 1, "status": 3, "isShadow": 0, "rssi": 0, "thingsModelValue": null, "activeTime": "2025-07-17", "createTime": "2025-07-17", "gwDevCode": null, "locationWay": 1, "imgUrl": "", "isOwner": 1, "subDeviceCount": 0, "slaveId": null, "transport": "MQTT", "protocolCode": "JSON", "deptName": "白云电器", "createBy": "bydq_admin", "canSeeCode": true, "alertCount": null, "guid": null, "thingsModels": null, "stringList": [], "integerList": [], "decimalList": [], "enumList": [], "arrayList": [], "boolList": [], "readOnlyList": []}, {"deviceId": 285, "deviceName": "旁路控制", "productId": 186, "productName": "旁路控制", "deviceType": 1, "tenantId": 19, "tenantName": "白云电器", "serialNumber": "D19Z5RLXI4YEV", "firmwareVersion": 1, "status": 3, "isShadow": 0, "rssi": 0, "thingsModelValue": null, "activeTime": "2025-07-17", "createTime": "2025-07-17", "gwDevCode": null, "locationWay": 1, "imgUrl": "", "isOwner": 1, "subDeviceCount": 0, "slaveId": null, "transport": "MQTT", "protocolCode": "JSON", "deptName": "白云电器", "createBy": "bydq_admin", "canSeeCode": true, "alertCount": null, "guid": null, "thingsModels": null, "stringList": [], "integerList": [], "decimalList": [], "enumList": [], "arrayList": [], "boolList": [], "readOnlyList": []}, {"deviceId": 284, "deviceName": "水冷系统", "productId": 185, "productName": "水冷系统", "deviceType": 1, "tenantId": 19, "tenantName": "白云电器", "serialNumber": "D19YFA0X66T51", "firmwareVersion": 1, "status": 3, "isShadow": 0, "rssi": 0, "thingsModelValue": null, "activeTime": "2025-07-17", "createTime": "2025-07-17", "gwDevCode": null, "locationWay": 1, "imgUrl": "", "isOwner": 1, "subDeviceCount": 0, "slaveId": null, "transport": "MQTT", "protocolCode": "JSON", "deptName": "白云电器", "createBy": "bydq_admin", "canSeeCode": true, "alertCount": null, "guid": null, "thingsModels": null, "stringList": [], "integerList": [], "decimalList": [], "enumList": [], "arrayList": [], "boolList": [], "readOnlyList": []}, {"deviceId": 283, "deviceName": "单元状态", "productId": 184, "productName": "单元状态", "deviceType": 1, "tenantId": 19, "tenantName": "白云电器", "serialNumber": "D199P305330O3", "firmwareVersion": 1, "status": 3, "isShadow": 0, "rssi": 0, "thingsModelValue": null, "activeTime": "2025-07-16", "createTime": "2025-07-16", "gwDevCode": null, "locationWay": 1, "imgUrl": "", "isOwner": 1, "subDeviceCount": 0, "slaveId": null, "transport": "MQTT", "protocolCode": "JSON", "deptName": "白云电器", "createBy": "bydq_admin", "canSeeCode": true, "alertCount": null, "guid": null, "thingsModels": null, "stringList": [], "integerList": [], "decimalList": [], "enumList": [], "arrayList": [], "boolList": [], "readOnlyList": []}, {"deviceId": 282, "deviceName": "版本信息", "productId": 183, "productName": "版本信息", "deviceType": 1, "tenantId": 19, "tenantName": "白云电器", "serialNumber": "D19LQX582805T", "firmwareVersion": 1, "status": 3, "isShadow": 0, "rssi": 0, "thingsModelValue": null, "activeTime": "2025-07-24", "createTime": "2025-07-10", "gwDevCode": null, "locationWay": 1, "imgUrl": "", "isOwner": 1, "subDeviceCount": 0, "slaveId": null, "transport": "MQTT", "protocolCode": "JSON", "deptName": "白云电器", "createBy": "bydq_admin", "canSeeCode": true, "alertCount": null, "guid": null, "thingsModels": null, "stringList": [], "integerList": [], "decimalList": [], "enumList": [], "arrayList": [], "boolList": [], "readOnlyList": []}]}